service:
  name: "api-gateway"
  version: "1.0.0"
  environment: "development"
  port: 8080
  client_id: "${API_GATEWAY_CLIENT_ID}"
  client_key: "${API_GATEWAY_CLIENT_KEY}"

downstream_services:
  user_service_addr: "user-service:50051"
  voucher_service_addr: "voucher-service:50051"
  product_service_addr: "product-service:50051"
  order_service_addr: "order-service:50051"
  notification_service_addr: "notification-service:50051"

auth:
  jwt_secret: "${JWT_SECRET_KEY}"
  jwt_expiration: "1h"

jaeger:
  host: "${JAEGER_HOST}"
  port: ${JAEGER_PORT}
  endpoint: "${JAEGER_ENDPOINT}"

metrics:
  enabled: ${METRICS_ENABLED}
  pushgateway_url: "${METRICS_PUSHGATEWAY_URL}"
  push_interval: "${METRICS_PUSH_INTERVAL}"
  instance_id: "${METRICS_INSTANCE_ID}"

logging:
  level: "debug"
  format: "json"
