# Coupon API Gateway

A high-performance HTTP API Gateway for the coupon microservice system, built with Go and Echo framework. This service acts as the single entry point for all client requests, handling authentication, routing, and API documentation.

## 🎯 Service Overview

The Coupon API Gateway provides:

- **HTTP REST API**: RESTful endpoints for all microservice operations
- **Authentication**: JWT token validation using HTTP-only cookies
- **Service Routing**: Intelligent routing to backend gRPC services
- **API Documentation**: Auto-generated OpenAPI 3.0 documentation with Tonic framework
- **Health Monitoring**: Comprehensive health checks and metrics
- **Request/Response Transformation**: Protocol translation between HTTP and gRPC

### Key Features

- **Cookie-based Authentication**: Secure JWT validation with HTTP-only cookies
- **gRPC Client Integration**: Seamless communication with 6 backend services
- **OpenAPI Documentation**: Interactive API documentation with Tonic framework
- **Middleware Stack**: Authentication, CORS, logging, and metrics middleware
- **Health Checks**: Service health monitoring and dependency checks
- **Metrics Collection**: Prometheus metrics for monitoring and alerting
- **Distributed Tracing**: Jaeger integration for request tracing

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Client    │───▶│   API Gateway    │───▶│ Backend Services│
│                 │    │                  │    │                 │
│ - React App     │    │ - HTTP REST API  │    │ - Auth Service  │
│ - Mobile App    │    │ - JWT Auth       │    │ - User Service  │
│ - Third Party   │    │ - gRPC Clients   │    │ - Product Svc   │
└─────────────────┘    │ - OpenAPI Docs   │    │ - Voucher Svc   │
                       │ - Tonic Framework│    │ - Order Service │
                       └──────────────────┘    │ - Notification  │
                                               └─────────────────┘
```

## 📋 API Endpoints

### Authentication
- `POST /api/login` - User authentication with email/password
- `POST /api/logout` - User logout (clears HTTP-only cookie)
- `GET /api/users/me` - Get current authenticated user profile

### User Management
- `POST /api/users` - Create new user account (registration)
- `GET /api/users/{id}` - Get user by ID
- `GET /api/users/email/{email}` - Get user by email address

### Product Catalog
- `GET /api/products` - List products with pagination, search, and filtering
- `GET /api/products/{id}` - Get detailed product information
- `PUT /api/products/{id}` - Update product information
- `GET /api/categories` - List all product categories

### Voucher Management
- `GET /api/vouchers` - List vouchers with filtering and pagination
- `POST /api/vouchers` - Create new voucher with restrictions
- `GET /api/vouchers/{id}` - Get voucher details with eligibility rules
- `GET /api/vouchers/code/{code}` - Get voucher by unique code
- `PUT /api/vouchers/{id}` - Update voucher configuration
- `POST /api/vouchers/check-eligibility` - Check voucher eligibility for user/order
- `GET /api/vouchers/auto-eligible` - Get auto-eligible vouchers for user
- `GET /api/discount-types` - List available discount types

### Order Processing
- `POST /api/orders` - Create new order with optional voucher application
- `GET /api/orders/{id}` - Get order details with voucher information
- `GET /api/orders` - List orders with pagination and filtering
- `GET /api/orders/voucher/{voucherId}` - List orders that used specific voucher
- `GET /api/users/{userId}/order-count` - Get user's total order count
- `GET /api/users/{userId}/voucher-usage/{voucherId}` - Get user's voucher usage count

### Notifications
- `POST /api/notifications` - Send notification to user
- `GET /api/notifications` - List user notifications with pagination
- `PUT /api/notifications/{id}/status` - Update notification read status

### System
- `GET /health` - Service health check with dependency status
- `GET /docs` - Interactive OpenAPI 3.0 documentation
- `GET /metrics` - Prometheus metrics endpoint

## 🔧 Configuration

### Environment Variables

```bash
# Service Configuration
API_GATEWAY_CLIENT_ID=api-gateway-client-id
API_GATEWAY_CLIENT_KEY=api-gateway-client-secret

# Downstream Services
AUTH_SERVICE_ADDR=auth-service:50051
USER_SERVICE_ADDR=user-service:50051
PRODUCT_SERVICE_ADDR=product-service:50051
VOUCHER_SERVICE_ADDR=voucher-service:50051
ORDER_SERVICE_ADDR=order-service:50051
NOTIFICATION_SERVICE_ADDR=notification-service:50051

# Observability
JAEGER_HOST=jaeger
JAEGER_PORT=6831
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Metrics
METRICS_ENABLED=true
METRICS_PUSHGATEWAY_URL=http://pushgateway:9091
METRICS_PUSH_INTERVAL=30s
METRICS_INSTANCE_ID=api-gateway-1
```

### Configuration File (`config/config.yaml`)

```yaml
service:
  name: "api-gateway"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${API_GATEWAY_CLIENT_ID}"
  client_key: "${API_GATEWAY_CLIENT_KEY}"

downstream_services:
  auth_service_addr: "auth-service:50051"
  user_service_addr: "user-service:50051"
  product_service_addr: "product-service:50051"
  voucher_service_addr: "voucher-service:50051"
  order_service_addr: "order-service:50051"
  notification_service_addr: "notification-service:50051"

jaeger:
  host: "${JAEGER_HOST}"
  port: ${JAEGER_PORT}
  endpoint: "${JAEGER_ENDPOINT}"

metrics:
  enabled: ${METRICS_ENABLED}
  pushgateway_url: "${METRICS_PUSHGATEWAY_URL}"
  push_interval: "${METRICS_PUSH_INTERVAL}"
  instance_id: "${METRICS_INSTANCE_ID}"

logging:
  level: "debug"
  format: "json"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"
```

## 🚀 Getting Started

### Prerequisites

- Go >= 1.24
- Docker and Docker Compose
- Access to backend microservices

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd coupon-api-gateway
   ```

2. **Install dependencies**
   ```bash
   go mod download
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Build and run the service**
   ```bash
   # Build the binary
   make build

   # Run the service locally
   make run
   ```

5. **Access the API**
   - API Base URL: `http://localhost:8080`
   - Health Check: `http://localhost:8080/health`
   - API Documentation: `http://localhost:8080/docs`
   - Metrics: `http://localhost:8080/metrics`

### Docker Deployment

1. **Build the Docker image**
   ```bash
   # Build Docker image
   make docker-build
   ```

2. **Run with Docker Compose**
   ```bash
   # Start the gateway with Jaeger tracing
   make compose-up
   ```

   This exposes the gateway on port `8080` and Jaeger UI on `16686`.

3. **Stop the services**
   ```bash
   make compose-down
   ```

## 🔐 Authentication

The API Gateway uses JWT-based authentication with HTTP-only cookies for enhanced security.

### Authentication Flow

1. **Login Request**: Client sends credentials to `/api/login`
2. **JWT Generation**: Gateway validates credentials with auth-service
3. **Cookie Setting**: JWT token is set as HTTP-only cookie
4. **Request Authentication**: Subsequent requests include cookie automatically
5. **Token Validation**: Gateway validates JWT on each protected endpoint

### Protected Endpoints

Most endpoints require authentication except:
- `POST /api/login`
- `POST /api/users` (registration)
- `GET /health`
- `GET /docs`
- `GET /metrics`

### Cookie Configuration

```go
cookie := &http.Cookie{
    Name:     "auth_token",
    Value:    token,
    Path:     "/",
    HttpOnly: true,
    Secure:   true, // HTTPS only in production
    SameSite: http.SameSiteStrictMode,
    MaxAge:   3600, // 1 hour
}
```

## 🔄 gRPC Client Integration

The gateway maintains persistent gRPC connections to all backend services:

### Client Configuration

```go
type Clients struct {
    Auth         *clients.AuthClient
    User         *clients.UserClient
    Product      *clients.ProductClient
    Voucher      *clients.VoucherClient
    Order        *clients.OrderClient
    Notification *clients.NotificationClient
}
```

### Service Authentication

All gRPC calls include service credentials:
- `client-id`: Service identifier (`api-gateway-client-id`)
- `client-key`: Service authentication key

### Connection Management

- **Connection Pooling**: Reuses connections for efficiency
- **Health Monitoring**: Monitors backend service health
- **Retry Logic**: Automatic retry on transient failures
- **Circuit Breaker**: Prevents cascade failures

## 📊 Monitoring and Observability

### Health Checks

The gateway provides comprehensive health monitoring:

```bash
curl http://localhost:8080/health
```

Response includes:
- Service status
- Backend service connectivity
- Memory and CPU usage
- Request metrics

### Metrics

Prometheus metrics available at `/metrics`:

- **HTTP Metrics**: Request count, duration, status codes
- **gRPC Metrics**: Backend service call metrics
- **Business Metrics**: Authentication success/failure rates
- **System Metrics**: Memory, CPU, goroutines

### Distributed Tracing

Jaeger tracing integration:
- Request tracing across services
- Performance bottleneck identification
- Error tracking and debugging

### Logging

Structured JSON logging with:
- Request/response logging
- Error tracking
- Performance metrics
- Security events

## 🧪 Testing

### Unit Tests

```bash
go test ./...
```

### Integration Tests

```bash
go test -tags=integration ./...
```

### API Testing

Use the OpenAPI documentation at `/docs` for interactive testing, or use curl:

```bash
# Login
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  -c cookies.txt

# Get user profile
curl -X GET http://localhost:8080/api/users/me \
  -b cookies.txt

# List products
curl -X GET "http://localhost:8080/api/products?page=1&limit=10" \
  -b cookies.txt

# Create order
curl -X POST http://localhost:8080/api/orders \
  -H "Content-Type: application/json" \
  -d '{"items":[{"product_id":1,"quantity":2}],"voucher_code":"SAVE10"}' \
  -b cookies.txt
```

### Load Testing

```bash
# Install k6
brew install k6

# Run load tests
k6 run scripts/load-test.js
```

## 🔧 Development

### Project Structure

```
coupon-api-gateway/
├── cmd/
│   └── server/
│       └── main.go              # Application entry point
├── internal/
│   ├── clients/                 # gRPC client implementations
│   │   ├── auth_client.go
│   │   ├── user_client.go
│   │   ├── product_client.go
│   │   ├── voucher_client.go
│   │   ├── order_client.go
│   │   └── notification_client.go
│   ├── handler/
│   │   └── http/               # HTTP request handlers
│   │       ├── auth_handler.go
│   │       ├── user_handler.go
│   │       ├── product_handler.go
│   │       ├── voucher_handler.go
│   │       ├── order_handler.go
│   │       └── notification_handler.go
│   ├── middleware/             # HTTP middleware
│   │   └── cookie_auth_middleware.go
│   └── model/                  # Data models and DTOs
├── config/
│   └── config.yaml             # Service configuration
├── scripts/                    # Deployment and utility scripts
├── Dockerfile                  # Container image definition
├── docker-compose.yml          # Local development setup
├── Makefile                    # Build and deployment commands
└── README.md                   # This file
```

### Adding New Endpoints

1. **Define Handler**: Create handler in `internal/handler/http/`
2. **Add Route**: Register route in `cmd/server/main.go`
3. **Add Client Call**: Implement gRPC client call if needed
4. **Update Documentation**: OpenAPI spec will auto-update with Tonic

### Middleware Development

Custom middleware should implement the Echo middleware interface:

```go
func CustomMiddleware() echo.MiddlewareFunc {
    return func(next echo.HandlerFunc) echo.HandlerFunc {
        return func(c echo.Context) error {
            // Middleware logic here
            return next(c)
        }
    }
}
```

## 🚨 Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "email",
      "reason": "invalid format"
    }
  },
  "request_id": "req_123456789",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

### Error Types

- **400 Bad Request**: Invalid request format or parameters
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server-side errors
- **502 Bad Gateway**: Backend service unavailable
- **503 Service Unavailable**: Service temporarily unavailable

## 🔒 Security

### Security Headers

```go
// Security headers middleware
c.Response().Header().Set("X-Content-Type-Options", "nosniff")
c.Response().Header().Set("X-Frame-Options", "DENY")
c.Response().Header().Set("X-XSS-Protection", "1; mode=block")
c.Response().Header().Set("Strict-Transport-Security", "max-age=31536000")
```

### CORS Configuration

```go
e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
    AllowOrigins:     []string{"https://yourdomain.com"},
    AllowMethods:     []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete},
    AllowHeaders:     []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept},
    AllowCredentials: true,
}))
```

### Rate Limiting

```go
e.Use(middleware.RateLimiter(middleware.NewRateLimiterMemoryStore(20)))
```

## 🐛 Troubleshooting

### Common Issues

1. **Backend Service Unavailable**
   ```bash
   # Check service connectivity
   curl http://backend-service:50051/health
   ```

2. **Authentication Failures**
   ```bash
   # Check JWT token in cookies
   curl -v http://localhost:8080/api/users/me
   ```

3. **High Memory Usage**
   ```bash
   # Check for memory leaks
   go tool pprof http://localhost:8080/debug/pprof/heap
   ```

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=debug
go run cmd/server/main.go
```

### Health Check Debugging

```bash
# Detailed health check
curl -v http://localhost:8080/health
```

## 📚 Additional Resources

- [Echo Framework Documentation](https://echo.labstack.com/)
- [gRPC Go Documentation](https://grpc.io/docs/languages/go/)
- [OpenAPI 3.0 Specification](https://swagger.io/specification/)
- [Tonic Framework](https://github.com/tonic-build/tonic)
- [Prometheus Metrics](https://prometheus.io/docs/concepts/metric_types/)
- [Jaeger Tracing](https://www.jaegertracing.io/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.