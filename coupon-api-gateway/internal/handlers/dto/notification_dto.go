package dto

import (
	"time"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_notification_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotificationResponse struct {
	ID          uint64             `json:"id"`
	UserID      uint64             `json:"user_id"`
	Type        NotificationType   `json:"type"`
	Title       string             `json:"title"`
	Message     string             `json:"message"`
	Status      NotificationStatus `json:"status"`
	ScheduledAt *time.Time         `json:"scheduled_at,omitempty"`
	SentAt      *time.Time         `json:"sent_at,omitempty"`
	ReadAt      *time.Time         `json:"read_at,omitempty"`
	CreatedAt   time.Time          `json:"created_at"`
}

type NotificationType string

const (
	NotificationTypeVoucherCreated    NotificationType = "VOUCHER_CREATED"
	NotificationTypeVoucherExpiring   NotificationType = "VOUCHER_EXPIRING"
	NotificationTypeVoucherUsed       NotificationType = "VOUCHER_USED"
	NotificationTypeOrderConfirmation NotificationType = "ORDER_CONFIRMATION"
	NotificationTypeVoucherApplied    NotificationType = "VOUCHER_APPLIED"
	NotificationTypeVoucherFailed     NotificationType = "VOUCHER_FAILED"
	NotificationTypeUserWelcome       NotificationType = "USER_WELCOME"
	NotificationTypeUserTypeUpgrade   NotificationType = "USER_TYPE_UPGRADE"
)

type NotificationStatus string

const (
	NotificationStatusPending   NotificationStatus = "PENDING"
	NotificationStatusSent      NotificationStatus = "SENT"
	NotificationStatusRead      NotificationStatus = "READ"
	NotificationStatusFailed    NotificationStatus = "FAILED"
	NotificationStatusCancelled NotificationStatus = "CANCELLED"
)

type UpdateNotificationStatusRequest struct {
	NotificationID uint64             `json:"notification_id" validate:"required"`
	Status         NotificationStatus `json:"status" validate:"required"`
}

type UpdateNotificationStatusResponse struct {
	Success bool `json:"success"`
}

func ToNotificationResponse(notification *proto_notification_v1.Notification) NotificationResponse {
	response := NotificationResponse{
		ID:        notification.Id,
		UserID:    notification.UserId,
		Type:      toNotificationType(notification.Type),
		Title:     notification.Title,
		Message:   notification.Message,
		Status:    toNotificationStatus(notification.Status),
		CreatedAt: notification.CreatedAt.AsTime(),
	}

	if notification.ScheduledAt != nil {
		scheduledAt := notification.ScheduledAt.AsTime()
		response.ScheduledAt = &scheduledAt
	}

	if notification.SentAt != nil {
		sentAt := notification.SentAt.AsTime()
		response.SentAt = &sentAt
	}

	if notification.ReadAt != nil {
		readAt := notification.ReadAt.AsTime()
		response.ReadAt = &readAt
	}

	return response
}

func toNotificationType(protoType proto_notification_v1.NotificationType) NotificationType {
	switch protoType {
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_CREATED:
		return NotificationTypeVoucherCreated
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_EXPIRING:
		return NotificationTypeVoucherExpiring
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_USED:
		return NotificationTypeVoucherUsed
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_ORDER_CONFIRMATION:
		return NotificationTypeOrderConfirmation
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_APPLIED:
		return NotificationTypeVoucherApplied
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_VOUCHER_FAILED:
		return NotificationTypeVoucherFailed
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_USER_WELCOME:
		return NotificationTypeUserWelcome
	case proto_notification_v1.NotificationType_NOTIFICATION_TYPE_USER_TYPE_UPGRADE:
		return NotificationTypeUserTypeUpgrade
	default:
		return NotificationTypeUserWelcome
	}
}

func toNotificationStatus(protoStatus proto_notification_v1.NotificationStatus) NotificationStatus {
	switch protoStatus {
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_PENDING:
		return NotificationStatusPending
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_SENT:
		return NotificationStatusSent
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_READ:
		return NotificationStatusRead
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_FAILED:
		return NotificationStatusFailed
	case proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_CANCELLED:
		return NotificationStatusCancelled
	default:
		return NotificationStatusPending
	}
}

func (r *UpdateNotificationStatusRequest) ToProtoRequest(userID uint64, requestID string) *proto_notification_v1.UpdateNotificationStatusRequest {
	return &proto_notification_v1.UpdateNotificationStatusRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   requestID,
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		NotificationId: r.NotificationID,
		Status:         fromNotificationStatus(r.Status),
	}
}

func fromNotificationStatus(status NotificationStatus) proto_notification_v1.NotificationStatus {
	switch status {
	case NotificationStatusPending:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_PENDING
	case NotificationStatusSent:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_SENT
	case NotificationStatusRead:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_READ
	case NotificationStatusFailed:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_FAILED
	case NotificationStatusCancelled:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_CANCELLED
	default:
		return proto_notification_v1.NotificationStatus_NOTIFICATION_STATUS_PENDING
	}
}
