package handlers

import (
	"fmt"
	"net/http"
	"time"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_notification_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotificationHandler struct {
	notificationClient *clients.NotificationClient
	logger             *logging.Logger
	validator          *validator.Validate
}

type listNotificationsResponse struct {
	Notifications []dto.NotificationResponse `json:"notifications"`
}

func NewNotificationHandler(notificationClient *clients.NotificationClient, logger *logging.Logger) *NotificationHandler {
	return &NotificationHandler{
		notificationClient: notificationClient,
		logger:             logger,
		validator:          validator.New(),
	}
}

func (h *NotificationHandler) RegisterProtectedRoutes(g *echo.Group, spec *docs.OpenApi) {
	listNotificationsRoute := g.GET("/notifications", h.HandleListNotifications)
	echoAdapter.AddRoute[struct{}, listNotificationsResponse](spec, listNotificationsRoute, docs.OperationObject{Tags: []string{"Notifications API"}})

	updateNotificationStatusRoute := g.PUT("/notifications/:id/status", h.HandleUpdateNotificationStatus)
	echoAdapter.AddRoute[dto.UpdateNotificationStatusRequest, dto.UpdateNotificationStatusResponse](spec, updateNotificationStatusRoute, docs.OperationObject{Tags: []string{"Notifications API"}})
}

type notificationIDParams struct {
	ID uint64 `param:"id" validate:"required"`
}

func (h *NotificationHandler) HandleListNotifications(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	grpcReq := &proto_notification_v1.ListNotificationsRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId:   fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()),
			UserId:      userID,
			Timestamp:   timestamppb.Now(),
			ServiceName: "api-gateway",
		},
		UserId: userID,
	}

	res, err := h.notificationClient.ListNotifications(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	notifications := make([]dto.NotificationResponse, len(res.Notifications))
	for i, notification := range res.Notifications {
		notifications[i] = dto.ToNotificationResponse(notification)
	}

	response := notifications

	return c.JSON(http.StatusOK, response)
}

func (h *NotificationHandler) HandleUpdateNotificationStatus(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	var params notificationIDParams
	if err := c.Bind(&params); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid notification ID"))
	}

	var req dto.UpdateNotificationStatusRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}

	req.NotificationID = params.ID

	if err := h.validator.Struct(req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	grpcReq := req.ToProtoRequest(userID, fmt.Sprintf("api-gateway-%d", time.Now().UnixNano()))

	_, err := h.notificationClient.UpdateNotificationStatus(c.Request().Context(), grpcReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	response := dto.UpdateNotificationStatusResponse{
		Success: true,
	}

	return c.JSON(http.StatusOK, response)
}
