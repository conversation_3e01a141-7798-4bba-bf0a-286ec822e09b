package clients

import (
	"context"
	"fmt"
	"time"

	notification_proto_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type NotificationClient struct {
	Client notification_proto_v1.NotificationServiceClient
	conn   *shared_grpc.Client
}

func NewNotificationClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, serviceName, clientID, clientKey string) (*NotificationClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, serviceName, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for notification service: %w", err)
	}
	return &NotificationClient{
		Client: notification_proto_v1.NewNotificationServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *NotificationClient) Close() {
	c.conn.Close()
}

func (c *NotificationClient) ListNotifications(ctx context.Context, req *notification_proto_v1.ListNotificationsRequest) (*notification_proto_v1.ListNotificationsResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	return c.Client.ListNotifications(ctx, req)
}

func (c *NotificationClient) UpdateNotificationStatus(ctx context.Context, req *notification_proto_v1.UpdateNotificationStatusRequest) (*notification_proto_v1.UpdateNotificationStatusResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	return c.Client.UpdateNotificationStatus(ctx, req)
}

func (c *NotificationClient) SendNotification(ctx context.Context, req *notification_proto_v1.SendNotificationRequest) (*notification_proto_v1.SendNotificationResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	return c.Client.SendNotification(ctx, req)
}
