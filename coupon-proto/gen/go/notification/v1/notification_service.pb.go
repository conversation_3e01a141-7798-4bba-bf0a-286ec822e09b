// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: notification/v1/notification_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NotificationStatus int32

const (
	NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED NotificationStatus = 0
	NotificationStatus_NOTIFICATION_STATUS_PENDING     NotificationStatus = 1
	NotificationStatus_NOTIFICATION_STATUS_SENT        NotificationStatus = 2
	NotificationStatus_NOTIFICATION_STATUS_FAILED      NotificationStatus = 3
	NotificationStatus_NOTIFICATION_STATUS_READ        NotificationStatus = 4
	NotificationStatus_NOTIFICATION_STATUS_CANCELLED   NotificationStatus = 5
)

// Enum value maps for NotificationStatus.
var (
	NotificationStatus_name = map[int32]string{
		0: "NOTIFICATION_STATUS_UNSPECIFIED",
		1: "NOTIFICATION_STATUS_PENDING",
		2: "NOTIFICATION_STATUS_SENT",
		3: "NOTIFICATION_STATUS_FAILED",
		4: "NOTIFICATION_STATUS_READ",
		5: "NOTIFICATION_STATUS_CANCELLED",
	}
	NotificationStatus_value = map[string]int32{
		"NOTIFICATION_STATUS_UNSPECIFIED": 0,
		"NOTIFICATION_STATUS_PENDING":     1,
		"NOTIFICATION_STATUS_SENT":        2,
		"NOTIFICATION_STATUS_FAILED":      3,
		"NOTIFICATION_STATUS_READ":        4,
		"NOTIFICATION_STATUS_CANCELLED":   5,
	}
)

func (x NotificationStatus) Enum() *NotificationStatus {
	p := new(NotificationStatus)
	*p = x
	return p
}

func (x NotificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_notification_v1_notification_service_proto_enumTypes[0].Descriptor()
}

func (NotificationStatus) Type() protoreflect.EnumType {
	return &file_notification_v1_notification_service_proto_enumTypes[0]
}

func (x NotificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationStatus.Descriptor instead.
func (NotificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{0}
}

type NotificationType int32

const (
	NotificationType_NOTIFICATION_TYPE_UNSPECIFIED        NotificationType = 0
	NotificationType_NOTIFICATION_TYPE_VOUCHER_CREATED    NotificationType = 1
	NotificationType_NOTIFICATION_TYPE_VOUCHER_EXPIRING   NotificationType = 2
	NotificationType_NOTIFICATION_TYPE_VOUCHER_USED       NotificationType = 3
	NotificationType_NOTIFICATION_TYPE_ORDER_CONFIRMATION NotificationType = 4
	NotificationType_NOTIFICATION_TYPE_VOUCHER_APPLIED    NotificationType = 5
	NotificationType_NOTIFICATION_TYPE_VOUCHER_FAILED     NotificationType = 6
	NotificationType_NOTIFICATION_TYPE_USER_WELCOME       NotificationType = 7
	NotificationType_NOTIFICATION_TYPE_USER_TYPE_UPGRADE  NotificationType = 8
)

// Enum value maps for NotificationType.
var (
	NotificationType_name = map[int32]string{
		0: "NOTIFICATION_TYPE_UNSPECIFIED",
		1: "NOTIFICATION_TYPE_VOUCHER_CREATED",
		2: "NOTIFICATION_TYPE_VOUCHER_EXPIRING",
		3: "NOTIFICATION_TYPE_VOUCHER_USED",
		4: "NOTIFICATION_TYPE_ORDER_CONFIRMATION",
		5: "NOTIFICATION_TYPE_VOUCHER_APPLIED",
		6: "NOTIFICATION_TYPE_VOUCHER_FAILED",
		7: "NOTIFICATION_TYPE_USER_WELCOME",
		8: "NOTIFICATION_TYPE_USER_TYPE_UPGRADE",
	}
	NotificationType_value = map[string]int32{
		"NOTIFICATION_TYPE_UNSPECIFIED":        0,
		"NOTIFICATION_TYPE_VOUCHER_CREATED":    1,
		"NOTIFICATION_TYPE_VOUCHER_EXPIRING":   2,
		"NOTIFICATION_TYPE_VOUCHER_USED":       3,
		"NOTIFICATION_TYPE_ORDER_CONFIRMATION": 4,
		"NOTIFICATION_TYPE_VOUCHER_APPLIED":    5,
		"NOTIFICATION_TYPE_VOUCHER_FAILED":     6,
		"NOTIFICATION_TYPE_USER_WELCOME":       7,
		"NOTIFICATION_TYPE_USER_TYPE_UPGRADE":  8,
	}
)

func (x NotificationType) Enum() *NotificationType {
	p := new(NotificationType)
	*p = x
	return p
}

func (x NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_notification_v1_notification_service_proto_enumTypes[1].Descriptor()
}

func (NotificationType) Type() protoreflect.EnumType {
	return &file_notification_v1_notification_service_proto_enumTypes[1]
}

func (x NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationType.Descriptor instead.
func (NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{1}
}

type Notification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Type          NotificationType       `protobuf:"varint,3,opt,name=type,proto3,enum=notification.v1.NotificationType" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Status        NotificationStatus     `protobuf:"varint,6,opt,name=status,proto3,enum=notification.v1.NotificationStatus" json:"status,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	SentAt        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	ReadAt        *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=read_at,json=readAt,proto3" json:"read_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notification) Reset() {
	*x = Notification{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{0}
}

func (x *Notification) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Notification) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Notification) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *Notification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notification) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Notification) GetStatus() NotificationStatus {
	if x != nil {
		return x.Status
	}
	return NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED
}

func (x *Notification) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Notification) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *Notification) GetReadAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadAt
	}
	return nil
}

func (x *Notification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type SendNotificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Type          NotificationType       `protobuf:"varint,2,opt,name=type,proto3,enum=notification.v1.NotificationType" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendNotificationRequest) Reset() {
	*x = SendNotificationRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationRequest) ProtoMessage() {}

func (x *SendNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendNotificationRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendNotificationRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendNotificationRequest) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_NOTIFICATION_TYPE_UNSPECIFIED
}

func (x *SendNotificationRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SendNotificationRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendNotificationRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

type SendNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notification  *Notification          `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendNotificationResponse) Reset() {
	*x = SendNotificationResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNotificationResponse) ProtoMessage() {}

func (x *SendNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNotificationResponse.ProtoReflect.Descriptor instead.
func (*SendNotificationResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{2}
}

func (x *SendNotificationResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendNotificationResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *SendNotificationResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type UpdateNotificationStatusRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	NotificationId uint64                 `protobuf:"varint,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	Status         NotificationStatus     `protobuf:"varint,3,opt,name=status,proto3,enum=notification.v1.NotificationStatus" json:"status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateNotificationStatusRequest) Reset() {
	*x = UpdateNotificationStatusRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationStatusRequest) ProtoMessage() {}

func (x *UpdateNotificationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotificationStatusRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateNotificationStatusRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateNotificationStatusRequest) GetNotificationId() uint64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *UpdateNotificationStatusRequest) GetStatus() NotificationStatus {
	if x != nil {
		return x.Status
	}
	return NotificationStatus_NOTIFICATION_STATUS_UNSPECIFIED
}

type UpdateNotificationStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notification  *Notification          `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotificationStatusResponse) Reset() {
	*x = UpdateNotificationStatusResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotificationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotificationStatusResponse) ProtoMessage() {}

func (x *UpdateNotificationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotificationStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateNotificationStatusResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateNotificationStatusResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateNotificationStatusResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *UpdateNotificationStatusResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationsRequest) Reset() {
	*x = ListNotificationsRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationsRequest) ProtoMessage() {}

func (x *ListNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationsRequest.ProtoReflect.Descriptor instead.
func (*ListNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListNotificationsRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListNotificationsRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ListNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notifications []*Notification        `protobuf:"bytes,2,rep,name=notifications,proto3" json:"notifications,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotificationsResponse) Reset() {
	*x = ListNotificationsResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotificationsResponse) ProtoMessage() {}

func (x *ListNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotificationsResponse.ProtoReflect.Descriptor instead.
func (*ListNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListNotificationsResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListNotificationsResponse) GetNotifications() []*Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *ListNotificationsResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type CreateNotificationFromTemplateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	TemplateKey   string                 `protobuf:"bytes,2,opt,name=template_key,json=templateKey,proto3" json:"template_key,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotificationFromTemplateRequest) Reset() {
	*x = CreateNotificationFromTemplateRequest{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotificationFromTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotificationFromTemplateRequest) ProtoMessage() {}

func (x *CreateNotificationFromTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotificationFromTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateNotificationFromTemplateRequest) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateNotificationFromTemplateRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateNotificationFromTemplateRequest) GetTemplateKey() string {
	if x != nil {
		return x.TemplateKey
	}
	return ""
}

func (x *CreateNotificationFromTemplateRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

type CreateNotificationFromTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Notification  *Notification          `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotificationFromTemplateResponse) Reset() {
	*x = CreateNotificationFromTemplateResponse{}
	mi := &file_notification_v1_notification_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotificationFromTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotificationFromTemplateResponse) ProtoMessage() {}

func (x *CreateNotificationFromTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_notification_v1_notification_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotificationFromTemplateResponse.ProtoReflect.Descriptor instead.
func (*CreateNotificationFromTemplateResponse) Descriptor() ([]byte, []int) {
	return file_notification_v1_notification_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateNotificationFromTemplateResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateNotificationFromTemplateResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *CreateNotificationFromTemplateResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_notification_v1_notification_service_proto protoreflect.FileDescriptor

const file_notification_v1_notification_service_proto_rawDesc = "" +
	"\n" +
	"*notification/v1/notification_service.proto\x12\x0fnotification.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xbf\x03\n" +
	"\fNotification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x04R\x06userId\x125\n" +
	"\x04type\x18\x03 \x01(\x0e2!.notification.v1.NotificationTypeR\x04type\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x12;\n" +
	"\x06status\x18\x06 \x01(\x0e2#.notification.v1.NotificationStatusR\x06status\x12=\n" +
	"\fscheduled_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\x123\n" +
	"\asent_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\x06sentAt\x123\n" +
	"\aread_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\x06readAt\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\xf7\x01\n" +
	"\x17SendNotificationRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x125\n" +
	"\x04type\x18\x02 \x01(\x0e2!.notification.v1.NotificationTypeR\x04type\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x12=\n" +
	"\fscheduled_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\"\xc5\x01\n" +
	"\x18SendNotificationResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12A\n" +
	"\fnotification\x18\x02 \x01(\v2\x1d.notification.v1.NotificationR\fnotification\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xbf\x01\n" +
	"\x1fUpdateNotificationStatusRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12'\n" +
	"\x0fnotification_id\x18\x02 \x01(\x04R\x0enotificationId\x12;\n" +
	"\x06status\x18\x03 \x01(\x0e2#.notification.v1.NotificationStatusR\x06status\"\xcd\x01\n" +
	" UpdateNotificationStatusResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12A\n" +
	"\fnotification\x18\x02 \x01(\v2\x1d.notification.v1.NotificationR\fnotification\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"k\n" +
	"\x18ListNotificationsRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x04R\x06userId\"\xc8\x01\n" +
	"\x19ListNotificationsResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12C\n" +
	"\rnotifications\x18\x02 \x03(\v2\x1d.notification.v1.NotificationR\rnotifications\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xc1\x01\n" +
	"%CreateNotificationFromTemplateRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\ftemplate_key\x18\x02 \x01(\tR\vtemplateKey\x12=\n" +
	"\fscheduled_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\"\xd3\x01\n" +
	"&CreateNotificationFromTemplateResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12A\n" +
	"\fnotification\x18\x02 \x01(\v2\x1d.notification.v1.NotificationR\fnotification\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error*\xd9\x01\n" +
	"\x12NotificationStatus\x12#\n" +
	"\x1fNOTIFICATION_STATUS_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bNOTIFICATION_STATUS_PENDING\x10\x01\x12\x1c\n" +
	"\x18NOTIFICATION_STATUS_SENT\x10\x02\x12\x1e\n" +
	"\x1aNOTIFICATION_STATUS_FAILED\x10\x03\x12\x1c\n" +
	"\x18NOTIFICATION_STATUS_READ\x10\x04\x12!\n" +
	"\x1dNOTIFICATION_STATUS_CANCELLED\x10\x05*\xec\x02\n" +
	"\x10NotificationType\x12!\n" +
	"\x1dNOTIFICATION_TYPE_UNSPECIFIED\x10\x00\x12%\n" +
	"!NOTIFICATION_TYPE_VOUCHER_CREATED\x10\x01\x12&\n" +
	"\"NOTIFICATION_TYPE_VOUCHER_EXPIRING\x10\x02\x12\"\n" +
	"\x1eNOTIFICATION_TYPE_VOUCHER_USED\x10\x03\x12(\n" +
	"$NOTIFICATION_TYPE_ORDER_CONFIRMATION\x10\x04\x12%\n" +
	"!NOTIFICATION_TYPE_VOUCHER_APPLIED\x10\x05\x12$\n" +
	" NOTIFICATION_TYPE_VOUCHER_FAILED\x10\x06\x12\"\n" +
	"\x1eNOTIFICATION_TYPE_USER_WELCOME\x10\a\x12'\n" +
	"#NOTIFICATION_TYPE_USER_TYPE_UPGRADE\x10\b2\xcd\x04\n" +
	"\x13NotificationService\x12g\n" +
	"\x10SendNotification\x12(.notification.v1.SendNotificationRequest\x1a).notification.v1.SendNotificationResponse\x12\x7f\n" +
	"\x18UpdateNotificationStatus\x120.notification.v1.UpdateNotificationStatusRequest\x1a1.notification.v1.UpdateNotificationStatusResponse\x12j\n" +
	"\x11ListNotifications\x12).notification.v1.ListNotificationsRequest\x1a*.notification.v1.ListNotificationsResponse\x12\x91\x01\n" +
	"\x1eCreateNotificationFromTemplate\x126.notification.v1.CreateNotificationFromTemplateRequest\x1a7.notification.v1.CreateNotificationFromTemplateResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB>Z<gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1b\x06proto3"

var (
	file_notification_v1_notification_service_proto_rawDescOnce sync.Once
	file_notification_v1_notification_service_proto_rawDescData []byte
)

func file_notification_v1_notification_service_proto_rawDescGZIP() []byte {
	file_notification_v1_notification_service_proto_rawDescOnce.Do(func() {
		file_notification_v1_notification_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_notification_v1_notification_service_proto_rawDesc), len(file_notification_v1_notification_service_proto_rawDesc)))
	})
	return file_notification_v1_notification_service_proto_rawDescData
}

var file_notification_v1_notification_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_notification_v1_notification_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_notification_v1_notification_service_proto_goTypes = []any{
	(NotificationStatus)(0),                        // 0: notification.v1.NotificationStatus
	(NotificationType)(0),                          // 1: notification.v1.NotificationType
	(*Notification)(nil),                           // 2: notification.v1.Notification
	(*SendNotificationRequest)(nil),                // 3: notification.v1.SendNotificationRequest
	(*SendNotificationResponse)(nil),               // 4: notification.v1.SendNotificationResponse
	(*UpdateNotificationStatusRequest)(nil),        // 5: notification.v1.UpdateNotificationStatusRequest
	(*UpdateNotificationStatusResponse)(nil),       // 6: notification.v1.UpdateNotificationStatusResponse
	(*ListNotificationsRequest)(nil),               // 7: notification.v1.ListNotificationsRequest
	(*ListNotificationsResponse)(nil),              // 8: notification.v1.ListNotificationsResponse
	(*CreateNotificationFromTemplateRequest)(nil),  // 9: notification.v1.CreateNotificationFromTemplateRequest
	(*CreateNotificationFromTemplateResponse)(nil), // 10: notification.v1.CreateNotificationFromTemplateResponse
	(*timestamppb.Timestamp)(nil),                  // 11: google.protobuf.Timestamp
	(*v1.RequestMetadata)(nil),                     // 12: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),                    // 13: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),                        // 14: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),                  // 15: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),                 // 16: common.v1.HealthCheckResponse
}
var file_notification_v1_notification_service_proto_depIdxs = []int32{
	1,  // 0: notification.v1.Notification.type:type_name -> notification.v1.NotificationType
	0,  // 1: notification.v1.Notification.status:type_name -> notification.v1.NotificationStatus
	11, // 2: notification.v1.Notification.scheduled_at:type_name -> google.protobuf.Timestamp
	11, // 3: notification.v1.Notification.sent_at:type_name -> google.protobuf.Timestamp
	11, // 4: notification.v1.Notification.read_at:type_name -> google.protobuf.Timestamp
	11, // 5: notification.v1.Notification.created_at:type_name -> google.protobuf.Timestamp
	12, // 6: notification.v1.SendNotificationRequest.metadata:type_name -> common.v1.RequestMetadata
	1,  // 7: notification.v1.SendNotificationRequest.type:type_name -> notification.v1.NotificationType
	11, // 8: notification.v1.SendNotificationRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	13, // 9: notification.v1.SendNotificationResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 10: notification.v1.SendNotificationResponse.notification:type_name -> notification.v1.Notification
	14, // 11: notification.v1.SendNotificationResponse.error:type_name -> common.v1.ServiceError
	12, // 12: notification.v1.UpdateNotificationStatusRequest.metadata:type_name -> common.v1.RequestMetadata
	0,  // 13: notification.v1.UpdateNotificationStatusRequest.status:type_name -> notification.v1.NotificationStatus
	13, // 14: notification.v1.UpdateNotificationStatusResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 15: notification.v1.UpdateNotificationStatusResponse.notification:type_name -> notification.v1.Notification
	14, // 16: notification.v1.UpdateNotificationStatusResponse.error:type_name -> common.v1.ServiceError
	12, // 17: notification.v1.ListNotificationsRequest.metadata:type_name -> common.v1.RequestMetadata
	13, // 18: notification.v1.ListNotificationsResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 19: notification.v1.ListNotificationsResponse.notifications:type_name -> notification.v1.Notification
	14, // 20: notification.v1.ListNotificationsResponse.error:type_name -> common.v1.ServiceError
	12, // 21: notification.v1.CreateNotificationFromTemplateRequest.metadata:type_name -> common.v1.RequestMetadata
	11, // 22: notification.v1.CreateNotificationFromTemplateRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	13, // 23: notification.v1.CreateNotificationFromTemplateResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 24: notification.v1.CreateNotificationFromTemplateResponse.notification:type_name -> notification.v1.Notification
	14, // 25: notification.v1.CreateNotificationFromTemplateResponse.error:type_name -> common.v1.ServiceError
	3,  // 26: notification.v1.NotificationService.SendNotification:input_type -> notification.v1.SendNotificationRequest
	5,  // 27: notification.v1.NotificationService.UpdateNotificationStatus:input_type -> notification.v1.UpdateNotificationStatusRequest
	7,  // 28: notification.v1.NotificationService.ListNotifications:input_type -> notification.v1.ListNotificationsRequest
	9,  // 29: notification.v1.NotificationService.CreateNotificationFromTemplate:input_type -> notification.v1.CreateNotificationFromTemplateRequest
	15, // 30: notification.v1.NotificationService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	4,  // 31: notification.v1.NotificationService.SendNotification:output_type -> notification.v1.SendNotificationResponse
	6,  // 32: notification.v1.NotificationService.UpdateNotificationStatus:output_type -> notification.v1.UpdateNotificationStatusResponse
	8,  // 33: notification.v1.NotificationService.ListNotifications:output_type -> notification.v1.ListNotificationsResponse
	10, // 34: notification.v1.NotificationService.CreateNotificationFromTemplate:output_type -> notification.v1.CreateNotificationFromTemplateResponse
	16, // 35: notification.v1.NotificationService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	31, // [31:36] is the sub-list for method output_type
	26, // [26:31] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_notification_v1_notification_service_proto_init() }
func file_notification_v1_notification_service_proto_init() {
	if File_notification_v1_notification_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_notification_v1_notification_service_proto_rawDesc), len(file_notification_v1_notification_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_notification_v1_notification_service_proto_goTypes,
		DependencyIndexes: file_notification_v1_notification_service_proto_depIdxs,
		EnumInfos:         file_notification_v1_notification_service_proto_enumTypes,
		MessageInfos:      file_notification_v1_notification_service_proto_msgTypes,
	}.Build()
	File_notification_v1_notification_service_proto = out.File
	file_notification_v1_notification_service_proto_goTypes = nil
	file_notification_v1_notification_service_proto_depIdxs = nil
}
