# Coupon Proto Definitions

The centralized Protocol Buffer definitions for the coupon microservice system. This repository serves as the single source of truth for all gRPC service contracts, message types, and event schemas across the entire platform.

## 🎯 Overview

The Coupon Proto repository provides:

- **gRPC Service Definitions**: Complete API contracts for all microservices
- **Message Types**: Standardized data structures across services
- **Event Schemas**: Kafka event message definitions
- **Common Types**: Shared types and utilities used across services
- **Code Generation**: Automated Go code generation with Buf
- **API Governance**: Linting and breaking change detection

## 📁 Repository Structure

```
coupon-proto/
├── auth/v1/                    # Authentication service definitions
│   ├── auth.proto             # Auth service gRPC API
│   └── auth_events.proto      # Auth-related events
├── user/v1/                   # User service definitions
│   ├── user.proto             # User service gRPC API
│   └── user_events.proto      # User lifecycle events
├── product/v1/                # Product service definitions
│   ├── product.proto          # Product service gRPC API
│   └── product_events.proto   # Product-related events
├── voucher/v1/                # Voucher service definitions
│   ├── voucher.proto          # Voucher service gRPC API
│   └── voucher_events.proto   # Voucher lifecycle events
├── order/v1/                  # Order service definitions
│   ├── order.proto            # Order service gRPC API
│   └── order_events.proto     # Order lifecycle events
├── notification/v1/           # Notification service definitions
│   ├── notification.proto     # Notification service gRPC API
│   └── notification_events.proto # Notification events
├── common/v1/                 # Shared definitions
│   ├── common.proto           # Common types and enums
│   ├── pagination.proto       # Pagination utilities
│   ├── error.proto            # Error definitions
│   └── timestamp.proto        # Time-related utilities
├── gen/                       # Generated Go code (auto-generated)
│   ├── auth/v1/
│   ├── user/v1/
│   ├── product/v1/
│   ├── voucher/v1/
│   ├── order/v1/
│   ├── notification/v1/
│   └── common/v1/
├── buf.yaml                   # Buf configuration
├── buf.gen.yaml              # Code generation configuration
├── buf.work.yaml             # Buf workspace configuration
└── scripts/                  # Build and utility scripts
```

## 🔧 Service Definitions

### Auth Service (`auth/v1/auth.proto`)
- `Login` - User authentication
- `ValidateToken` - JWT token validation
- `GetUserFromToken` - Extract user info from token
- `RegisterService` - Service registration
- `ValidateServiceCredentials` - Service authentication
- `HealthCheck` - Service health status

### User Service (`user/v1/user.proto`)
- `CreateUser` - User registration
- `GetUser` - Get user by ID
- `GetUserByEmail` - Get user by email
- `UpdateUser` - Update user profile
- `DeleteUser` - Soft delete user
- `ListUsers` - List users with pagination
- `AuthenticateUser` - Validate credentials
- `HealthCheck` - Service health status

### Product Service (`product/v1/product.proto`)
- `GetProduct` - Get product by ID
- `UpdateProduct` - Update product details
- `ListProducts` - List products with filtering
- `ListCategories` - List product categories
- `HealthCheck` - Service health status

### Voucher Service (`voucher/v1/voucher.proto`)
- `CreateVoucher` - Create new voucher
- `GetVoucher` - Get voucher by ID
- `GetVoucherByCode` - Get voucher by code
- `UpdateVoucher` - Update voucher details
- `DeleteVoucher` - Soft delete voucher
- `ListVouchers` - List vouchers with pagination
- `ValidateVoucher` - Validate voucher for use
- `CheckEligibility` - Check user eligibility
- `UseVoucher` - Redeem voucher
- `GetUserVouchers` - Get user's available vouchers
- `HealthCheck` - Service health status

### Order Service (`order/v1/order.proto`)
- `CreateOrder` - Create new order
- `GetOrder` - Get order by ID
- `ListOrders` - List user orders
- `UpdateOrderStatus` - Update order status
- `HealthCheck` - Service health status

### Notification Service (`notification/v1/notification.proto`)
- `SendNotification` - Send notification to user
- `GetNotification` - Get notification by ID
- `ListNotifications` - List user notifications
- `UpdateNotificationStatus` - Mark as read/unread
- `HealthCheck` - Service health status

## 📨 Event Schemas

### User Events (`user/v1/user_events.proto`)
```protobuf
message UserCreatedEvent {
  int32 user_id = 1;
  string email = 2;
  string user_type = 3;
  google.protobuf.Timestamp created_at = 4;
}

message UserUpdatedEvent {
  int32 user_id = 1;
  string email = 2;
  string user_type = 3;
  repeated string changed_fields = 4;
  google.protobuf.Timestamp updated_at = 5;
}
```

### Voucher Events (`voucher/v1/voucher_events.proto`)
```protobuf
message VoucherCreatedEvent {
  int32 voucher_id = 1;
  string code = 2;
  string discount_type = 3;
  double discount_value = 4;
  google.protobuf.Timestamp created_at = 5;
}

message VoucherUsedEvent {
  int32 voucher_id = 1;
  int32 user_id = 2;
  int32 order_id = 3;
  double discount_amount = 4;
  google.protobuf.Timestamp used_at = 5;
}
```

### Order Events (`order/v1/order_events.proto`)
```protobuf
message OrderCreatedEvent {
  int32 order_id = 1;
  int32 user_id = 2;
  double total_amount = 3;
  double discount_amount = 4;
  double final_amount = 5;
  optional int32 voucher_id = 6;
  google.protobuf.Timestamp created_at = 7;
}
```

## 🛠️ Toolchain

### Buf CLI
- **Linting**: Ensures consistent style and best practices
- **Breaking Change Detection**: Prevents API compatibility issues
- **Code Generation**: Generates Go code from proto definitions
- **Dependency Management**: Manages proto dependencies

### Protocol Buffer Plugins
- **protoc-gen-go**: Generates Go structs and serialization code
- **protoc-gen-go-grpc**: Generates Go gRPC client and server code
- **protoc-gen-grpc-gateway**: Generates HTTP/JSON gateway (if needed)
## 🚀 Getting Started

### Prerequisites

- Go 1.24+
- Buf CLI
- Protocol Buffer compiler (protoc)
- Required protoc plugins

### Installation

1. **Install Buf and dependencies**
   ```bash
   # Run the installation script
   chmod +x scripts/install-deps.sh
   make install-deps
   ```

2. **Verify installation**
   ```bash
   buf --version
   protoc --version
   ```

### Development Workflow

1. **Make changes to proto files**
   ```bash
   # Edit the relevant .proto files
   vim auth/v1/auth.proto
   ```

2. **Generate code**
   ```bash
   # Generate Go code from proto definitions
   make generate
   ```

3. **Lint and validate**
   ```bash
   # Lint all proto files
   make lint

   # Check for breaking changes
   make breaking
   ```

4. **Test generated code**
   ```bash
   # Build generated code
   make build

   # Run tests
   make test
   ```

## 🔧 Makefile Commands

### Development Commands

```bash
# Install dependencies (buf, protoc plugins)
make install-deps

# Generate Go code from proto files
make generate

# Lint all proto files
make lint

# Check for breaking changes
make breaking

# Build generated Go code
make build

# Run tests
make test

# Clean generated files
make clean

# Format proto files
make format

# Update dependencies
make deps-update
```

### CI/CD Commands

```bash
# Run all checks (lint, breaking, build, test)
make ci

# Generate and push updated code
make release
```

## 📋 Common Message Types

### Pagination (`common/v1/pagination.proto`)
```protobuf
message PaginationRequest {
  int32 page = 1;
  int32 limit = 2;
  string sort_by = 3;
  string sort_order = 4; // "asc" or "desc"
}

message PaginationResponse {
  int32 total_count = 1;
  int32 page = 2;
  int32 limit = 3;
  int32 total_pages = 4;
  bool has_next = 5;
  bool has_prev = 6;
}
```

### Error Handling (`common/v1/error.proto`)
```protobuf
message ErrorDetail {
  string code = 1;
  string message = 2;
  string field = 3;
  map<string, string> metadata = 4;
}

message ErrorResponse {
  string error_code = 1;
  string error_message = 2;
  repeated ErrorDetail details = 3;
  string request_id = 4;
  google.protobuf.Timestamp timestamp = 5;
}
```

### Common Types (`common/v1/common.proto`)
```protobuf
enum UserType {
  USER_TYPE_UNSPECIFIED = 0;
  USER_TYPE_REGULAR = 1;
  USER_TYPE_PREMIUM = 2;
  USER_TYPE_VIP = 3;
}

enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;
  ORDER_STATUS_PENDING = 1;
  ORDER_STATUS_CONFIRMED = 2;
  ORDER_STATUS_PROCESSING = 3;
  ORDER_STATUS_SHIPPED = 4;
  ORDER_STATUS_DELIVERED = 5;
  ORDER_STATUS_CANCELLED = 6;
  ORDER_STATUS_REFUNDED = 7;
}

enum DiscountType {
  DISCOUNT_TYPE_UNSPECIFIED = 0;
  DISCOUNT_TYPE_PERCENTAGE = 1;
  DISCOUNT_TYPE_FIXED_AMOUNT = 2;
}
```

## 🔄 Code Generation

### Buf Configuration (`buf.gen.yaml`)

```yaml
version: v1
plugins:
  - plugin: buf.build/protocolbuffers/go
    out: gen
    opt:
      - paths=source_relative
  - plugin: buf.build/grpc/go
    out: gen
    opt:
      - paths=source_relative
      - require_unimplemented_servers=false
```

### Generated Code Structure

```
gen/
├── auth/v1/
│   ├── auth.pb.go              # Message types
│   └── auth_grpc.pb.go         # gRPC client/server
├── user/v1/
│   ├── user.pb.go
│   ├── user_grpc.pb.go
│   └── user_events.pb.go       # Event message types
├── common/v1/
│   ├── common.pb.go
│   ├── pagination.pb.go
│   └── error.pb.go
└── ...
```

## 🧪 Testing

### Proto Validation

```bash
# Validate all proto files
buf lint

# Check for breaking changes against main
buf breaking --against '.git#branch=main'

# Build check (compile proto files)
buf build
```

### Generated Code Testing

```bash
# Test generated Go code compiles
go build ./gen/...

# Run unit tests for generated code
go test ./gen/...
```

## 📏 Style Guide

### Naming Conventions

- **Services**: PascalCase (e.g., `UserService`, `AuthService`)
- **Methods**: PascalCase (e.g., `CreateUser`, `GetOrder`)
- **Messages**: PascalCase (e.g., `CreateUserRequest`, `UserResponse`)
- **Fields**: snake_case (e.g., `user_id`, `created_at`)
- **Enums**: SCREAMING_SNAKE_CASE (e.g., `USER_TYPE_PREMIUM`)

### Message Design

- Always include request/response message pairs
- Use optional fields for nullable values
- Include pagination for list operations
- Add timestamps for audit trails
- Use enums for predefined values

### Service Design

- Keep services focused on single domains
- Include health check methods
- Use consistent error handling
- Add proper documentation comments

## 🔒 Security Considerations

### Authentication

All gRPC services should implement authentication:

```protobuf
service UserService {
  // Requires authentication
  rpc GetUser(GetUserRequest) returns (GetUserResponse);

  // Public endpoint (no auth required)
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}
```

### Data Validation

- Use field validation annotations
- Implement proper input sanitization
- Add length limits for string fields
- Use appropriate numeric ranges

## 🚨 Breaking Changes

### What Constitutes a Breaking Change

- Removing fields, services, or methods
- Changing field types or numbers
- Renaming fields, services, or methods
- Changing method signatures
- Modifying enum values

### Safe Changes

- Adding new fields (with appropriate defaults)
- Adding new services or methods
- Adding new enum values
- Adding optional fields
- Improving documentation

### Migration Strategy

1. **Deprecation**: Mark old fields/methods as deprecated
2. **Dual Support**: Support both old and new versions
3. **Migration Period**: Allow time for clients to migrate
4. **Removal**: Remove deprecated elements in next major version

## 🤝 Contributing

### Development Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/add-new-service
   ```

2. **Make Changes**
   - Add/modify proto files
   - Update documentation
   - Add tests if needed

3. **Validate Changes**
   ```bash
   make lint
   make breaking
   make generate
   make build
   ```

4. **Submit Pull Request**
   - Include clear description
   - Document any breaking changes
   - Update CHANGELOG.md

### Code Review Checklist

- [ ] Proto files follow style guide
- [ ] No breaking changes (or properly documented)
- [ ] Generated code compiles successfully
- [ ] Documentation is updated
- [ ] Tests pass
- [ ] Buf linting passes

## 📚 Additional Resources

- [Protocol Buffers Documentation](https://developers.google.com/protocol-buffers)
- [gRPC Documentation](https://grpc.io/docs/)
- [Buf Documentation](https://docs.buf.build/)
- [Protocol Buffer Style Guide](https://developers.google.com/protocol-buffers/docs/style)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
