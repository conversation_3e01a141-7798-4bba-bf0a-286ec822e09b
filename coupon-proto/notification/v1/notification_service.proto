syntax = "proto3";

package notification.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/timestamp.proto";

option
  go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/notification/v1";

service NotificationService {
  rpc SendNotification(SendNotificationRequest) 
    returns (SendNotificationResponse);
  rpc UpdateNotificationStatus(UpdateNotificationStatusRequest)
    returns (UpdateNotificationStatusResponse);
  rpc ListNotifications(ListNotificationsRequest)
    returns (ListNotificationsResponse);

  rpc CreateNotificationFromTemplate(CreateNotificationFromTemplateRequest)
    returns (CreateNotificationFromTemplateResponse);

  rpc HealthCheck(common.v1.HealthCheckRequest)
    returns (common.v1.HealthCheckResponse);
}

enum NotificationStatus {
  NOTIFICATION_STATUS_UNSPECIFIED = 0;
  NOTIFICATION_STATUS_PENDING = 1;
  NOTIFICATION_STATUS_SENT = 2;
  NOTIFICATION_STATUS_FAILED = 3;
  NOTIFICATION_STATUS_READ = 4;
  NOTIFICATION_STATUS_CANCELLED = 5;
}

enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  NOTIFICATION_TYPE_VOUCHER_CREATED = 1;
  NOTIFICATION_TYPE_VOUCHER_EXPIRING = 2;
  NOTIFICATION_TYPE_VOUCHER_USED = 3;
  NOTIFICATION_TYPE_ORDER_CONFIRMATION = 4;
  NOTIFICATION_TYPE_VOUCHER_APPLIED = 5;
  NOTIFICATION_TYPE_VOUCHER_FAILED = 6;
  NOTIFICATION_TYPE_USER_WELCOME = 7;
  NOTIFICATION_TYPE_USER_TYPE_UPGRADE = 8;
}

message Notification {
  uint64 id = 1;
  uint64 user_id = 2;
  NotificationType type = 3;
  string title = 4;
  string message = 5;
  NotificationStatus status = 6;
  google.protobuf.Timestamp scheduled_at = 7;
  google.protobuf.Timestamp sent_at = 8;
  google.protobuf.Timestamp read_at = 9;
  google.protobuf.Timestamp created_at = 10;
}

message SendNotificationRequest {
  common.v1.RequestMetadata metadata = 1;
  NotificationType type = 2;
  string title = 3;
  string message = 4;
  google.protobuf.Timestamp scheduled_at = 5;
}

message SendNotificationResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

message UpdateNotificationStatusRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 notification_id = 2;
  NotificationStatus status = 3;
}

message UpdateNotificationStatusResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}

message ListNotificationsRequest {
  common.v1.RequestMetadata metadata = 1;
  uint64 user_id = 2;
}

message ListNotificationsResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Notification notifications = 2;
  common.v1.ServiceError error = 3;
}

message CreateNotificationFromTemplateRequest {
  common.v1.RequestMetadata metadata = 1;
  string template_key = 2;
  google.protobuf.Timestamp scheduled_at = 3;
}

message CreateNotificationFromTemplateResponse {
  common.v1.ResponseMetadata metadata = 1;
  Notification notification = 2;
  common.v1.ServiceError error = 3;
}
