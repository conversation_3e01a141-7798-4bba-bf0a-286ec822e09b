services:
  postgres-notification:
    image: postgres:16-alpine
    container_name: postgres-notification
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-notification_db}
    ports:
      - "5438:5432"
    volumes:
      - postgres-notification-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-notification_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-notification:
    image: redis:7-alpine
    container_name: redis-notification
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6385:6379"
    volumes:
      - redis-notification-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  notification-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: notification-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-notification-service
    depends_on:
      postgres-notification:
        condition: service_healthy
      redis-notification:
        condition: service_healthy
    env_file:
      - .env
    ports:
      - "8086:8080"
      - "50057:50051"
    restart: unless-stopped
    networks:
      - coupon-network
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres-notification-data:
  redis-notification-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
