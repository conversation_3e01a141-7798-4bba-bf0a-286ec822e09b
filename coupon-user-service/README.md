# Coupon User Service

A comprehensive user management service for the coupon microservice system, built with Go and gRPC. This service handles user registration, authentication, profile management, and publishes user events to Kafka for other services to consume.

## 🎯 Service Overview

The Coupon User Service provides:

- **User Registration**: New user account creation with validation
- **User Authentication**: Login and credential validation
- **Profile Management**: User profile updates and information retrieval
- **User Lookup**: Find users by ID, email, or other criteria
- **Event Publishing**: Kafka events for user lifecycle changes
- **Health Monitoring**: Comprehensive health checks and metrics

### Key Features

- **User CRUD Operations**: Complete user lifecycle management
- **Password Security**: bcrypt hashing for secure password storage
- **User Type Management**: Support for different user categories (REGULAR, PREMIUM, VIP)
- **Event-Driven Architecture**: Kafka integration for user events
- **Database Integration**: PostgreSQL with GORM for data persistence and auto-migration
- **gRPC Authentication Middleware**: Service-to-service authentication
- **Distributed Tracing**: <PERSON><PERSON>ger integration for request tracing
- **Metrics Collection**: Prometheus metrics for monitoring and alerting
- **Health Checks**: Service health monitoring with dependency checks

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│   User Service   │───▶│   PostgreSQL    │
│                 │    │                  │    │                 │
│ - User APIs     │    │ - User CRUD      │    │ - Users Table   │
│ - Registration  │    │ - Authentication │    │ - User Profiles │
│ - Profile Mgmt  │    │ - Profile Mgmt   │    │ - Audit Logs    │
└─────────────────┘    │ - Event Publish  │    └─────────────────┘
                       │ - Health Checks  │
┌─────────────────┐    └──────────────────┘    ┌─────────────────┐
│ Other Services  │                            │     Kafka       │
│                 │◀───────────────────────────│                 │
│ - Voucher Svc   │    User Events             │ - User Created  │
│ - Order Service │    (user.created,          │ - User Updated  │
│ - Notification  │     user.updated)          │ - User Deleted  │
└─────────────────┘                            └─────────────────┘
```

## 📋 gRPC API Methods

### User Management
- `CreateUser(CreateUserRequest) → CreateUserResponse` - Register new user account
- `GetUser(GetUserRequest) → GetUserResponse` - Get user by ID
- `GetUserByEmail(GetUserByEmailRequest) → GetUserByEmailResponse` - Get user by email address
- `UpdateUser(UpdateUserRequest) → UpdateUserResponse` - Update user profile information
- `DeleteUser(DeleteUserRequest) → DeleteUserResponse` - Soft delete user account
- `ListUsers(ListUsersRequest) → ListUsersResponse` - List users with pagination

### Authentication
- `AuthenticateUser(AuthenticateUserRequest) → AuthenticateUserResponse` - Validate user credentials

### Health Check
- `HealthCheck(HealthCheckRequest) → HealthCheckResponse` - Service health status

## 🔧 Configuration

### Environment Variables

```bash
# Service Configuration
USER_SERVICE_CLIENT_ID=user-service-client-id
USER_SERVICE_CLIENT_KEY=user-service-client-secret

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=coupon_user
POSTGRES_SSLMODE=disable

# Auth Service Configuration
AUTH_SERVICE_ADDR=auth-service:50051

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_TOPIC_USER_EVENTS=user-events

# Observability
JAEGER_HOST=jaeger
JAEGER_PORT=6831
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Metrics
METRICS_ENABLED=true
METRICS_PUSHGATEWAY_URL=http://pushgateway:9091
METRICS_PUSH_INTERVAL=30s
METRICS_INSTANCE_ID=user-service-1
```

### Configuration File (`config/config.yaml`)

```yaml
service:
  name: "user-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${USER_SERVICE_CLIENT_ID}"
  client_key: "${USER_SERVICE_CLIENT_KEY}"

database:
  host: "${POSTGRES_HOST}"
  port: ${POSTGRES_PORT}
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  dbname: "${POSTGRES_DB}"
  sslmode: "${POSTGRES_SSLMODE}"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

auth_service:
  addr: "${AUTH_SERVICE_ADDR}"

kafka:
  brokers: ["${KAFKA_BROKERS}"]
  topic_user_events: "${KAFKA_TOPIC_USER_EVENTS}"
  producer_config:
    retry_max: 3
    retry_backoff: "100ms"
    flush_timeout: "10s"

jaeger:
  host: "${JAEGER_HOST}"
  port: ${JAEGER_PORT}
  endpoint: "${JAEGER_ENDPOINT}"

metrics:
  enabled: ${METRICS_ENABLED}
  pushgateway_url: "${METRICS_PUSHGATEWAY_URL}"
  push_interval: "${METRICS_PUSH_INTERVAL}"
  instance_id: "${METRICS_INSTANCE_ID}"

logging:
  level: "debug"
  format: "json"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"
```
## 🚀 Getting Started

### Prerequisites

- Go >= 1.24
- PostgreSQL 13+
- Kafka 2.8+
- Docker and Docker Compose

### Local Development

1. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Build and run**
   ```bash
   # Build the binary
   make build

   # Run the service locally
   make run
   ```

3. **Access the service**
   - gRPC Server: `localhost:50051`
   - HTTP Health Check: `http://localhost:8080/health`
   - Metrics: `http://localhost:8080/metrics`

### Docker Deployment

```bash
# Start with all dependencies
make compose-up

# Stop services
make compose-down
```

## 🗄️ Database Schema

### Users Table
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    user_type VARCHAR(50) DEFAULT 'REGULAR',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### User Types
- `REGULAR` - Standard user account
- `PREMIUM` - Premium user with enhanced features
- `VIP` - VIP user with maximum privileges

## 📨 Kafka Events

### Published Events

#### User Created Event
```json
{
  "event_type": "user.created",
  "user_id": 123,
  "email": "<EMAIL>",
  "user_type": "REGULAR",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

#### User Updated Event
```json
{
  "event_type": "user.updated",
  "user_id": 123,
  "email": "<EMAIL>",
  "user_type": "PREMIUM",
  "changes": ["user_type", "first_name"],
  "timestamp": "2023-12-01T10:00:00Z"
}
```

#### User Deleted Event
```json
{
  "event_type": "user.deleted",
  "user_id": 123,
  "email": "<EMAIL>",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 🧪 Testing

### Unit Tests
```bash
make test
```

### gRPC Testing
```bash
# Create user
grpcurl -plaintext -d '{"email":"<EMAIL>","password":"password123","first_name":"John","last_name":"Doe"}' \
  localhost:50051 user.UserService/CreateUser

# Get user by email
grpcurl -plaintext -d '{"email":"<EMAIL>"}' \
  localhost:50051 user.UserService/GetUserByEmail

# Authenticate user
grpcurl -plaintext -d '{"email":"<EMAIL>","password":"password123"}' \
  localhost:50051 user.UserService/AuthenticateUser
```

## 📊 Monitoring

### Health Checks
```bash
curl http://localhost:8080/health
```

### Metrics
- User registration rates
- Authentication success/failure rates
- Database connection pool stats
- Kafka producer metrics
- gRPC request metrics

## 🔧 Development

### Project Structure
```
coupon-user-service/
├── cmd/server/main.go           # Application entry point
├── internal/
│   ├── service/user_service.go  # Core business logic
│   ├── repository/user_repo.go  # Data access layer
│   ├── model/user.go           # User data model
│   ├── middleware/grpc_auth.go # Authentication middleware
│   └── kafka/producer.go       # Kafka event publisher
├── config/config.yaml          # Service configuration
├── Dockerfile                  # Container image
├── docker-compose.yml          # Local development
└── Makefile                    # Build commands
```

### Adding New User Fields

1. Update the User model in `internal/model/user.go`
2. Add database migration (GORM auto-migration handles this)
3. Update gRPC proto definitions
4. Update service methods in `internal/service/user_service.go`
5. Update Kafka event payloads if needed

## 🚨 Error Handling

### gRPC Error Codes
- `OK` - Successful operation
- `INVALID_ARGUMENT` - Invalid request parameters
- `ALREADY_EXISTS` - User already exists (duplicate email)
- `NOT_FOUND` - User not found
- `UNAUTHENTICATED` - Invalid credentials
- `INTERNAL` - Internal server error

## 🔒 Security Features

- **Password Hashing**: bcrypt with cost factor 12
- **Email Validation**: RFC 5322 compliant email validation
- **Input Sanitization**: SQL injection prevention
- **Service Authentication**: gRPC middleware with client credentials
- **Audit Logging**: User action logging for security monitoring

## 📚 Additional Resources

- [gRPC Go Documentation](https://grpc.io/docs/languages/go/)
- [GORM Documentation](https://gorm.io/docs/)
- [Kafka Go Client](https://github.com/segmentio/kafka-go)
- [bcrypt Documentation](https://pkg.go.dev/golang.org/x/crypto/bcrypt)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
