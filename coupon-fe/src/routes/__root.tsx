import {
  Outlet,
  createR<PERSON><PERSON>out<PERSON>,
  useRouterState,
  useNavigate,
} from "@tanstack/react-router";
import { useState, createContext, useEffect } from "react";
import { Sidebar } from "@/components/Sidebar";
import { Header } from "@/components/Header";
import { UserHeader } from "@/components/UserHeader";
import { useUser } from "@/services/user";
import { AppSkeleton } from "@/components/AppSkeleton";

export type LayoutContextType = {
  activeTab: string;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
};

export const LayoutContext = createContext<LayoutContextType>({
  activeTab: "dashboard",
  setActiveTab: () => {},
});

function RootComponent() {
  const [activeTab, setActiveTab] = useState("dashboard");
  const pathname = useRouterState({ select: (s) => s.location.pathname });
  const navigate = useNavigate();
  const user = useUser();
  const isUser = user.data?.role?.toLowerCase() === "user";

  useEffect(() => {
    if (user.isError && pathname !== "/login") {
      navigate({ to: "/login" });
    }
  }, [user.isError, pathname, navigate]);

  useEffect(() => {
    if (pathname === "/login" && user.data) {
      navigate({ to: isUser ? "/products" : "/" });
    }
  }, [pathname, user.data, navigate, isUser]);

  useEffect(() => {
    if (!user.data) return;
    if (isUser && pathname === "/") {
      navigate({ to: "/products" });
    }
    if (!isUser && pathname === "/products") {
      navigate({ to: "/" });
    }
  }, [isUser, pathname, navigate, user.data]);

  if (pathname !== "/login" && (user.isError || user.isPending)) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <AppSkeleton />
      </div>
    );
  }

  return (
    <LayoutContext.Provider value={{ activeTab, setActiveTab }}>
      {pathname === "/login" ? (
        <Outlet />
      ) : isUser ? (
        <div className="min-h-screen flex flex-col w-full bg-gray-50">
          <UserHeader />
          <main className="flex-1">
            <Outlet />
          </main>
        </div>
      ) : (
        <div className="min-h-screen flex w-full bg-gray-50">
          <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
          <div className="flex flex-col flex-1">
            <Header activeTab={activeTab} />
            <main className="flex-1">
              <Outlet />
            </main>
          </div>
        </div>
      )}
    </LayoutContext.Provider>
  );
}

export const Route = createRootRoute({
  component: RootComponent,
});
