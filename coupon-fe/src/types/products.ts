import type { paths } from "@/openapi-spec";
import type { CamelCased } from "@/lib/camelCase";

export type RawProduct = NonNullable<
  paths["/api/products"]["get"]["responses"][200]["content"]["application/json"]["data"]
>[number];

export type RawProductListResponse =
  paths["/api/products"]["get"]["responses"][200]["content"]["application/json"];
export type Product = CamelCased<RawProduct> & {
  id: number;
  name: string;
  imageUrl: string;
  price: number;
};

export interface ProductListResponse
  extends Omit<CamelCased<RawProductListResponse>, "data"> {
  data?: Product[];
}

export type RawProductDetailResponse =
  paths["/api/products/{id}"]["get"]["responses"][200]["content"]["application/json"];
export type ProductDetailResponse = Product;
