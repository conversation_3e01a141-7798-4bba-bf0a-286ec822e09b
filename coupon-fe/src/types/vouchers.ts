import type { paths } from "@/openapi-spec";
import type { CamelCased } from "@/lib/camelCase";

export type RawVoucher =
  paths["/api/vouchers/{id}"]["get"]["responses"][200]["content"]["application/json"];
export type Voucher = CamelCased<RawVoucher>;

export type RawVoucherListResponse =
  paths["/api/vouchers"]["get"]["responses"][200]["content"]["application/json"];
export type VoucherListResponse = CamelCased<RawVoucherListResponse>;

export type RawVoucherListItem = NonNullable<
  paths["/api/vouchers"]["get"]["responses"][200]["content"]["application/json"]["data"]
>[number];
export type VoucherListItem = CamelCased<RawVoucherListItem>;

export interface ProductRestriction {
  id?: number;
  voucherId?: number;
  productId?: number;
  productName?: string;
  categoryId?: number;
  categoryName?: string;
  isIncluded?: boolean;
  createdAt?: string;
}

export interface TimeRestriction {
  id?: number;
  voucherId?: number;
  allowedDaysOfWeek?: number[];
  allowedHoursStart?: number;
  allowedHoursEnd?: number;
  recurrenceDayOfMonth?: number;
  recurrenceDayOfWeek?: number;
  recurrenceMonth?: number;
  recurrencePattern?: string;
  restrictionType?: string;
  specificDates?: string[];
  timezone?: string;
  createdAt?: string;
}

export interface UserVoucherUsage {
  userId?: number;
  usageCount?: number;
  fullName?: string;
  email?: string;
  type?: string;
  orders?: VoucherUsageOrder[];
}

export interface VoucherUsageOrder {
  orderId?: number;
  usedAt?: string;
  orderAmount?: number;
  status?: string;
}

export type VoucherUsageHistoryRecord = VoucherUsageOrder & {
  userId?: number;
  fullName?: string;
  email?: string;
  type?: string;
};

export interface UserEligibilityRule {
  id?: number;
  voucherId?: number;
  createdAt?: string;
  maxAccountAgeDays?: number;
  maxPreviousOrders?: number;
  minAccountAgeDays?: number;
  minPreviousOrders?: number;
  userId?: number;
  userType?: string;
}

export type RawCheckEligibilityRequest = NonNullable<
  paths["/api/vouchers/check-eligibility"]["post"]["requestBody"]
>["content"]["application/json"];
export type CheckEligibilityRequest = CamelCased<RawCheckEligibilityRequest>;

export type RawCheckEligibilityResponse =
  paths["/api/vouchers/check-eligibility"]["post"]["responses"][200]["content"]["application/json"];
export type CheckEligibilityResponse = CamelCased<RawCheckEligibilityResponse>;

export type RawAutoEligibilityRequest = NonNullable<
  paths["/api/vouchers/auto-eligible"]["post"]["requestBody"]
>["content"]["application/json"];
export type AutoEligibilityRequest = CamelCased<RawAutoEligibilityRequest>;

export type RawEligibleAutoVoucher = NonNullable<
  paths["/api/vouchers/auto-eligible"]["post"]["responses"][200]["content"]["application/json"]
>[number];
export type EligibleAutoVoucher = CamelCased<RawEligibleAutoVoucher>;

export type RawUpdateVoucherRequest = NonNullable<
  NonNullable<
    paths["/api/vouchers/{id}"]["put"]["requestBody"]
  >["content"]["application/json"]
>;
export type UpdateVoucherRequest = CamelCased<RawUpdateVoucherRequest>;
