import type { paths } from "@/openapi-spec";
import type { CamelCased } from "@/lib/camelCase";

export type RawOrder = NonNullable<
  paths["/api/orders"]["get"]["responses"][200]["content"]["application/json"]["data"]
>[number];

export type RawOrderListResponse =
  paths["/api/orders"]["get"]["responses"][200]["content"]["application/json"];
export type OrderListResponse = CamelCased<RawOrderListResponse>;

export type Order = CamelCased<RawOrder>;

export type RawCreateOrderRequest = NonNullable<
  NonNullable<
    paths["/api/orders"]["post"]["requestBody"]
  >["content"]["application/json"]
>;

export type CreateOrderRequest = CamelCased<RawCreateOrderRequest>;
