export type NotificationType = 
  | "VOUCHER_CREATED"
  | "VOUCHER_EXPIRING"
  | "VOUCHER_USED"
  | "ORDER_CONFIRMATION"
  | "VOUCHER_APPLIED"
  | "VOUCHER_FAILED"
  | "USER_WELCOME"
  | "USER_TYPE_UPGRADE";

export type NotificationStatus = 
  | "PENDING"
  | "SENT"
  | "READ"
  | "FAILED"
  | "CANCELLED";

export interface NotificationResponse {
  id: number;
  userId: number;
  type: NotificationType;
  title: string;
  message: string;
  status: NotificationStatus;
  scheduledAt?: string;
  sentAt?: string;
  readAt?: string;
  createdAt: string;
}

export interface ListNotificationsRequest {
}

export interface ListNotificationsResponse {
  notifications: NotificationResponse[];
}

export interface UpdateNotificationStatusRequest {
  notificationId: number;
  status: NotificationStatus;
}

export interface UpdateNotificationStatusResponse {
  success: boolean;
}

// UI-specific notification interface for the NotificationBell component
export interface UINotification {
  id: string;
  type: 'message' | 'like' | 'comment' | 'system';
  title: string;
  description: string;
  timestamp: string;
  isRead: boolean;
  avatar?: string;
}

// Helper function to convert API notification to UI notification
export function convertToUINotification(notification: NotificationResponse): UINotification {
  const getUIType = (type: NotificationType): 'message' | 'like' | 'comment' | 'system' => {
    switch (type) {
      case 'VOUCHER_CREATED':
      case 'VOUCHER_EXPIRING':
      case 'VOUCHER_APPLIED':
        return 'like';
      case 'VOUCHER_USED':
      case 'ORDER_CONFIRMATION':
        return 'comment';
      case 'USER_WELCOME':
      case 'USER_TYPE_UPGRADE':
        return 'message';
      case 'VOUCHER_FAILED':
      default:
        return 'system';
    }
  };

  const formatTimestamp = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  };

  return {
    id: notification.id.toString(),
    type: getUIType(notification.type),
    title: notification.title,
    description: notification.message,
    timestamp: formatTimestamp(notification.createdAt),
    isRead: notification.status === 'READ',
  };
}
