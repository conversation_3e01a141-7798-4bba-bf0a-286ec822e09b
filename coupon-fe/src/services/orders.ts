import { $api } from "./common";
import { snakeCaseKeys } from "@/lib/snakeCase";
import type { OrderListResponse, CreateOrderRequest } from "@/types/orders";

export interface ListOrdersParams {
  page?: number;
  limit?: number;
  search?: string;
}

export function useOrdersList(
  params: ListOrdersParams = { page: 1, limit: 10 },
) {
  const query = $api.useQuery("get", "/api/orders", {
    params: { query: snakeCaseKeys(params) },
  });
  return { ...query, data: query.data as OrderListResponse | undefined };
}

export function useCreateOrder() {
  const mutation = $api.useMutation("post", "/api/orders");
  type Variables = { body: CreateOrderRequest } & Record<string, unknown>;
  return {
    ...mutation,
    mutate: (
      variables: Variables,
      options?: Parameters<typeof mutation.mutate>[1],
    ) =>
      mutation.mutate(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
    mutateAsync: (
      variables: Variables,
      options?: Parameters<typeof mutation.mutateAsync>[1],
    ) =>
      mutation.mutateAsync(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
  };
}

export function useOrder(id: number) {
  return $api.useQuery("get", "/api/orders/{id}", { params: { path: { id } } });
}
