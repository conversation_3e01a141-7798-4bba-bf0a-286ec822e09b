import { $api } from "./common";
import { snakeCaseKeys } from "@/lib/snakeCase";
import type {
  ListNotificationsResponse,
  UpdateNotificationStatusRequest
} from "@/types/notifications";

export function useListNotifications() {
  const query = $api.useQuery("get", "/api/notifications");
  return {
    ...query,
    data: query.data as ListNotificationsResponse | undefined
  };
}

export function useUpdateNotificationStatus() {
  const mutation = $api.useMutation("put", "/api/notifications/{id}/status");

  return {
    ...mutation,
    mutate: (
      variables: { id: number; body: UpdateNotificationStatusRequest },
      options?: Parameters<typeof mutation.mutate>[1],
    ) => {
      // The API expects the notification ID both in the URL path and in the request body
      const requestBody = {
        ...variables.body,
        notificationId: variables.id
      };

      return mutation.mutate(
        {
          params: { path: { id: variables.id } },
          body: snakeCaseKeys(requestBody)
        } as any,
        options,
      );
    },
    mutateAsync: (
      variables: { id: number; body: UpdateNotificationStatusRequest },
      options?: Parameters<typeof mutation.mutateAsync>[1],
    ) => {
      // The API expects the notification ID both in the URL path and in the request body
      const requestBody = {
        ...variables.body,
        notificationId: variables.id
      };

      return mutation.mutateAsync(
        {
          params: { path: { id: variables.id } },
          body: snakeCaseKeys(requestBody)
        } as any,
        options,
      );
    },
  };
}


