import { $api } from "./common";
import { snakeCaseKeys } from "@/lib/snakeCase";
import type {
  VoucherListResponse,
  Voucher,
  AutoEligibilityRequest,
  CheckEligibilityRequest,
  UpdateVoucherRequest,
} from "@/types/vouchers";

export interface ListVouchersParams {
  sortOrder?: string;
  page?: number;
  limit?: number;
  search?: string;
  discountTypeId?: number;
  usageMethod?: string;
  status?: string;
  sortBy?: string;
}

export function useVouchersList(
  params: ListVouchersParams = {
    page: 1,
    limit: 10,
    sortBy: "created_at",
    sortOrder: "desc",
  },
) {
  const query = $api.useQuery("get", "/api/vouchers", {
    params: { query: snakeCaseKeys(params) },
  });
  return {
    ...query,
    data: query.data as VoucherListResponse | undefined,
  };
}

export function useVoucher(id: number) {
  const query = $api.useQuery("get", "/api/vouchers/{id}", {
    params: { path: { id } },
  });
  return { ...query, data: query.data as Voucher | undefined };
}

export function useCreateVoucher() {
  return $api.useMutation("post", "/api/vouchers");
}

export function useUpdateVoucher() {
  const mutation = $api.useMutation("put", "/api/vouchers/{id}");
  type Variables = {
    params: { path: { id: number } };
    body: UpdateVoucherRequest;
  };
  return {
    ...mutation,
    mutate: (
      variables: Variables,
      options?: Parameters<typeof mutation.mutate>[1],
    ) =>
      mutation.mutate(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
    mutateAsync: (
      variables: Variables,
      options?: Parameters<typeof mutation.mutateAsync>[1],
    ) =>
      mutation.mutateAsync(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
  };
}

export function useDiscountTypes() {
  return $api.useQuery("get", "/api/vouchers/discount-types");
}

export function useAvailableVouchers(
  params: Omit<ListVouchersParams, "status"> = { page: 1, limit: 10 },
) {
  const query = $api.useQuery("get", "/api/vouchers", {
    params: { query: snakeCaseKeys({ ...params, status: "ACTIVE" }) },
  });
  return {
    ...query,
    data: query.data as VoucherListResponse | undefined,
  };
}

export function useEligibleAutoVouchers() {
  const mutation = $api.useMutation("post", "/api/vouchers/auto-eligible");
  return {
    ...mutation,
    mutate: (
      variables: { body: AutoEligibilityRequest } & Record<string, unknown>,
      options?: Parameters<typeof mutation.mutate>[1],
    ) =>
      mutation.mutate(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
    mutateAsync: (
      variables: { body: AutoEligibilityRequest } & Record<string, unknown>,
      options?: Parameters<typeof mutation.mutateAsync>[1],
    ) =>
      mutation.mutateAsync(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
  };
}

export function useCheckVoucherEligibility() {
  const mutation = $api.useMutation("post", "/api/vouchers/check-eligibility");
  return {
    ...mutation,
    mutate: (
      variables: { body: CheckEligibilityRequest } & Record<string, unknown>,
      options?: Parameters<typeof mutation.mutate>[1],
    ) =>
      mutation.mutate(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
    mutateAsync: (
      variables: { body: CheckEligibilityRequest } & Record<string, unknown>,
      options?: Parameters<typeof mutation.mutateAsync>[1],
    ) =>
      mutation.mutateAsync(
        { ...variables, body: snakeCaseKeys(variables.body) },
        options,
      ),
  };
}
