/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as IndexRouteImport } from './routes/index'
import { Route as VouchersIndexRouteImport } from './routes/vouchers/index'
import { Route as ProductsIndexRouteImport } from './routes/products/index'
import { Route as OrdersIndexRouteImport } from './routes/orders/index'
import { Route as VouchersIdRouteImport } from './routes/vouchers/$id'
import { Route as OrdersCreateRouteImport } from './routes/orders/create'
import { Route as VouchersIdIndexRouteImport } from './routes/vouchers/$id/index'
import { Route as VouchersIdEditRouteImport } from './routes/vouchers/$id/edit'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const VouchersIndexRoute = VouchersIndexRouteImport.update({
  id: '/vouchers/',
  path: '/vouchers/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsIndexRoute = ProductsIndexRouteImport.update({
  id: '/products/',
  path: '/products/',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersIndexRoute = OrdersIndexRouteImport.update({
  id: '/orders/',
  path: '/orders/',
  getParentRoute: () => rootRouteImport,
} as any)
const VouchersIdRoute = VouchersIdRouteImport.update({
  id: '/vouchers/$id',
  path: '/vouchers/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersCreateRoute = OrdersCreateRouteImport.update({
  id: '/orders/create',
  path: '/orders/create',
  getParentRoute: () => rootRouteImport,
} as any)
const VouchersIdIndexRoute = VouchersIdIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => VouchersIdRoute,
} as any)
const VouchersIdEditRoute = VouchersIdEditRouteImport.update({
  id: '/edit',
  path: '/edit',
  getParentRoute: () => VouchersIdRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/orders/create': typeof OrdersCreateRoute
  '/vouchers/$id': typeof VouchersIdRouteWithChildren
  '/orders': typeof OrdersIndexRoute
  '/products': typeof ProductsIndexRoute
  '/vouchers': typeof VouchersIndexRoute
  '/vouchers/$id/edit': typeof VouchersIdEditRoute
  '/vouchers/$id/': typeof VouchersIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/orders/create': typeof OrdersCreateRoute
  '/orders': typeof OrdersIndexRoute
  '/products': typeof ProductsIndexRoute
  '/vouchers': typeof VouchersIndexRoute
  '/vouchers/$id/edit': typeof VouchersIdEditRoute
  '/vouchers/$id': typeof VouchersIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/orders/create': typeof OrdersCreateRoute
  '/vouchers/$id': typeof VouchersIdRouteWithChildren
  '/orders/': typeof OrdersIndexRoute
  '/products/': typeof ProductsIndexRoute
  '/vouchers/': typeof VouchersIndexRoute
  '/vouchers/$id/edit': typeof VouchersIdEditRoute
  '/vouchers/$id/': typeof VouchersIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/orders/create'
    | '/vouchers/$id'
    | '/orders'
    | '/products'
    | '/vouchers'
    | '/vouchers/$id/edit'
    | '/vouchers/$id/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/orders/create'
    | '/orders'
    | '/products'
    | '/vouchers'
    | '/vouchers/$id/edit'
    | '/vouchers/$id'
  id:
    | '__root__'
    | '/'
    | '/login'
    | '/orders/create'
    | '/vouchers/$id'
    | '/orders/'
    | '/products/'
    | '/vouchers/'
    | '/vouchers/$id/edit'
    | '/vouchers/$id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  LoginRoute: typeof LoginRoute
  OrdersCreateRoute: typeof OrdersCreateRoute
  VouchersIdRoute: typeof VouchersIdRouteWithChildren
  OrdersIndexRoute: typeof OrdersIndexRoute
  ProductsIndexRoute: typeof ProductsIndexRoute
  VouchersIndexRoute: typeof VouchersIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/vouchers/': {
      id: '/vouchers/'
      path: '/vouchers'
      fullPath: '/vouchers'
      preLoaderRoute: typeof VouchersIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products/': {
      id: '/products/'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders/': {
      id: '/orders/'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof OrdersIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/vouchers/$id': {
      id: '/vouchers/$id'
      path: '/vouchers/$id'
      fullPath: '/vouchers/$id'
      preLoaderRoute: typeof VouchersIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders/create': {
      id: '/orders/create'
      path: '/orders/create'
      fullPath: '/orders/create'
      preLoaderRoute: typeof OrdersCreateRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/vouchers/$id/': {
      id: '/vouchers/$id/'
      path: '/'
      fullPath: '/vouchers/$id/'
      preLoaderRoute: typeof VouchersIdIndexRouteImport
      parentRoute: typeof VouchersIdRoute
    }
    '/vouchers/$id/edit': {
      id: '/vouchers/$id/edit'
      path: '/edit'
      fullPath: '/vouchers/$id/edit'
      preLoaderRoute: typeof VouchersIdEditRouteImport
      parentRoute: typeof VouchersIdRoute
    }
  }
}

interface VouchersIdRouteChildren {
  VouchersIdEditRoute: typeof VouchersIdEditRoute
  VouchersIdIndexRoute: typeof VouchersIdIndexRoute
}

const VouchersIdRouteChildren: VouchersIdRouteChildren = {
  VouchersIdEditRoute: VouchersIdEditRoute,
  VouchersIdIndexRoute: VouchersIdIndexRoute,
}

const VouchersIdRouteWithChildren = VouchersIdRoute._addFileChildren(
  VouchersIdRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  LoginRoute: LoginRoute,
  OrdersCreateRoute: OrdersCreateRoute,
  VouchersIdRoute: VouchersIdRouteWithChildren,
  OrdersIndexRoute: OrdersIndexRoute,
  ProductsIndexRoute: ProductsIndexRoute,
  VouchersIndexRoute: VouchersIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
