import { useState, useEffect, useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  CalendarIcon,
  Plus,
  Trash2,
  Receipt,
  Save,
  X,
  Check,
  RefreshCw,
  Gift,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useCreateOrder } from "@/services/orders";
import { LayoutContext } from "@/routes/__root";
import { useNavigate } from "@tanstack/react-router";

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
}

interface Coupon {
  id: string;
  code: string;
  description: string;
  discount: number;
  status: "auto-applied" | "available" | "not-available";
  badge: string;
  savings: number;
  icon: string;
  bgColor: string;
  textColor: string;
}

const CreateOrder = () => {
  const { setActiveTab } = useContext(LayoutContext);
  const navigate = useNavigate();
  useEffect(() => {
    setActiveTab("orders");
  }, [setActiveTab]);

  const createOrder = useCreateOrder();

  const [customerName, setCustomerName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [orderDate, setOrderDate] = useState<Date>();
  const [status, setStatus] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("");
  const [orderItems, setOrderItems] = useState<OrderItem[]>([
    { name: "", quantity: 1, price: 0 },
  ]);
  const [manualCouponCode, setManualCouponCode] = useState("");
  const [appliedCoupons, setAppliedCoupons] = useState<Coupon[]>([
    {
      id: "save20",
      code: "SAVE20",
      description: "20% off orders over ₫200,000",
      discount: 440000,
      status: "auto-applied",
      badge: "Best Match",
      savings: 440000,
      icon: "%",
      bgColor: "bg-green-50",
      textColor: "text-green-600",
    },
  ]);
  const [orderNotes, setOrderNotes] = useState("");

  const suggestedCoupons: Coupon[] = [
    {
      id: "save20",
      code: "SAVE20",
      description: "20% off orders over ₫200,000",
      discount: 440000,
      status: "auto-applied",
      badge: "Best Match",
      savings: 440000,
      icon: "%",
      bgColor: "bg-green-50",
      textColor: "text-green-600",
    },
    {
      id: "freeship",
      code: "FREESHIP",
      description: "Free shipping on all orders",
      discount: 25000,
      status: "available",
      badge: "Available",
      savings: 25000,
      icon: "gift",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600",
    },
    {
      id: "newuser50",
      code: "NEWUSER50",
      description: "₫50,000 off for new customers",
      discount: 50000,
      status: "available",
      badge: "First Order",
      savings: 50000,
      icon: "star",
      bgColor: "bg-purple-50",
      textColor: "text-purple-600",
    },
    {
      id: "weekend30",
      code: "WEEKEND30",
      description: "30% off weekend orders",
      discount: 0,
      status: "not-available",
      badge: "Not Eligible",
      savings: 0,
      icon: "clock",
      bgColor: "bg-gray-50",
      textColor: "text-gray-400",
    },
  ];

  const subtotal = orderItems.reduce(
    (sum, item) => sum + item.quantity * item.price,
    0,
  );
  const totalDiscount = appliedCoupons.reduce(
    (sum, coupon) => sum + coupon.discount,
    0,
  );
  const tax = (subtotal - totalDiscount) * 0.1;
  const total = subtotal - totalDiscount + tax;

  const addOrderItem = () => {
    setOrderItems([...orderItems, { name: "", quantity: 1, price: 0 }]);
  };

  const removeOrderItem = (index: number) => {
    setOrderItems(orderItems.filter((_, i) => i !== index));
  };

  const updateOrderItem = (
    index: number,
    field: keyof OrderItem,
    value: string | number,
  ) => {
    const updatedItems = orderItems.map((item, i) =>
      i === index ? { ...item, [field]: value } : item,
    );
    setOrderItems(updatedItems);
  };

  const applyCoupon = (coupon: Coupon) => {
    if (
      coupon.status === "available" &&
      !appliedCoupons.find((c) => c.id === coupon.id)
    ) {
      setAppliedCoupons([
        ...appliedCoupons,
        { ...coupon, status: "auto-applied" },
      ]);
    }
  };

  const removeCoupon = (couponId: string) => {
    setAppliedCoupons(appliedCoupons.filter((c) => c.id !== couponId));
  };

  const handleVerifyManualCoupon = () => {
    console.log("Verifying coupon:", manualCouponCode);
  };

  const handleCreateOrder = async () => {
    await createOrder.mutateAsync({
      body: {
        coupon_code: manualCouponCode || undefined,
        order_amount: total,
        order_timestamp: orderDate?.toISOString() as unknown as Record<
          string,
          never
        >,
      },
    });
    navigate({ to: "/orders" });
  };

  const handleSaveAsDraft = () => {
    console.log("Saving order as draft");
  };

  const getCouponIcon = (iconType: string) => {
    switch (iconType) {
      case "%":
        return (
          <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 text-sm font-bold">
            %
          </div>
        );
      case "gift":
        return <Gift className="w-6 h-6 text-blue-600" />;
      case "star":
        return (
          <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 text-sm">
            ★
          </div>
        );
      case "clock":
        return (
          <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 text-sm">
            ○
          </div>
        );
      default:
        return <Receipt className="w-6 h-6" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Order Details Card */}
          <div className="lg:col-span-2">
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-800">
                  <Receipt className="h-5 w-5 text-blue-600" />
                  Order Details
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Fill in the order information below
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Customer Information */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Customer Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        placeholder="Customer Name"
                        value={customerName}
                        onChange={(e) => setCustomerName(e.target.value)}
                        className="bg-white"
                      />
                    </div>
                    <div>
                      <Input
                        placeholder="Email Address"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="bg-white"
                      />
                    </div>
                    <div>
                      <Input
                        placeholder="Phone Number"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        className="bg-white"
                      />
                    </div>
                  </div>
                </div>

                {/* Order Information */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Order Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal bg-white",
                              !orderDate && "text-muted-foreground",
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {orderDate
                              ? format(orderDate, "dd/MM/yyyy, --:--")
                              : "dd/mm/yyyy, --:--"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={orderDate}
                            onSelect={setOrderDate}
                            initialFocus
                            className="pointer-events-auto"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div>
                      <Select value={status} onValueChange={setStatus}>
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Pending" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="processing">Processing</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="md:col-span-2">
                      <Select
                        value={paymentMethod}
                        onValueChange={setPaymentMethod}
                      >
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Select Payment Method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="card">Credit Card</SelectItem>
                          <SelectItem value="zalopay">ZaloPay</SelectItem>
                          <SelectItem value="bank">Bank Transfer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">
                      Order Items
                    </h3>
                    <Button
                      onClick={addOrderItem}
                      className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 h-8"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Item
                    </Button>
                  </div>
                  <div className="space-y-3">
                    {orderItems.map((item, index) => (
                      <div
                        key={index}
                        className="grid grid-cols-12 gap-3 items-center"
                      >
                        <div className="col-span-5">
                          <Input
                            placeholder="Item name"
                            value={item.name}
                            onChange={(e) =>
                              updateOrderItem(index, "name", e.target.value)
                            }
                            className="bg-white"
                          />
                        </div>
                        <div className="col-span-2">
                          <Input
                            placeholder="Qty"
                            type="number"
                            value={item.quantity}
                            onChange={(e) =>
                              updateOrderItem(
                                index,
                                "quantity",
                                parseInt(e.target.value) || 0,
                              )
                            }
                            className="bg-white"
                          />
                        </div>
                        <div className="col-span-4">
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                              ₫
                            </span>
                            <Input
                              placeholder="Price"
                              type="number"
                              value={item.price}
                              onChange={(e) =>
                                updateOrderItem(
                                  index,
                                  "price",
                                  parseFloat(e.target.value) || 0,
                                )
                              }
                              className="bg-white pl-8"
                            />
                          </div>
                        </div>
                        <div className="col-span-1">
                          {orderItems.length > 1 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeOrderItem(index)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Coupon Code */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-700">
                      Auto-Suggested Coupons
                    </h3>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-blue-600">
                        Auto-Apply Enabled
                      </span>
                      <RefreshCw className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                  <div className="space-y-3">
                    {suggestedCoupons.map((coupon) => (
                      <div
                        key={coupon.id}
                        className={cn("p-4 rounded-lg border", coupon.bgColor)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {getCouponIcon(coupon.icon)}
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-semibold text-gray-900">
                                  {coupon.code}
                                </span>
                                <span
                                  className={cn(
                                    "px-2 py-1 rounded text-xs font-medium",
                                    coupon.status === "auto-applied"
                                      ? "bg-green-100 text-green-700"
                                      : coupon.status === "available"
                                        ? "bg-blue-100 text-blue-700"
                                        : "bg-gray-100 text-gray-500",
                                  )}
                                >
                                  {coupon.badge}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">
                                {coupon.description}
                              </p>
                              <p className="text-sm font-medium text-green-600 mt-1">
                                Saves ₫{coupon.savings.toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div>
                            {coupon.status === "auto-applied" ? (
                              <div className="flex items-center text-green-600">
                                <Check className="h-4 w-4 mr-1" />
                                <span className="text-sm font-medium">
                                  Auto-Applied
                                </span>
                              </div>
                            ) : coupon.status === "available" ? (
                              <Button
                                onClick={() => applyCoupon(coupon)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2"
                              >
                                Apply
                              </Button>
                            ) : (
                              <span className="text-sm text-gray-400 font-medium">
                                Not Available
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Manual Coupon Entry */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Manual Coupon Entry
                  </h3>
                  <div className="flex gap-3">
                    <div className="flex-1 relative">
                      <Receipt className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Enter coupon code manually"
                        value={manualCouponCode}
                        onChange={(e) => setManualCouponCode(e.target.value)}
                        className="bg-white pl-10"
                      />
                    </div>
                    <Button
                      onClick={handleVerifyManualCoupon}
                      variant="outline"
                      className="px-6"
                    >
                      Verify & Apply
                    </Button>
                  </div>
                </div>

                {/* Applied Coupons */}
                {appliedCoupons.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-medium text-gray-700">
                        Applied Coupons
                      </h3>
                      <span className="text-sm text-gray-500">
                        Max 3 coupons
                      </span>
                    </div>
                    <div className="space-y-2">
                      {appliedCoupons.map((coupon) => (
                        <div
                          key={coupon.id}
                          className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg"
                        >
                          <div className="flex items-center gap-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="font-medium text-gray-900">
                              {coupon.code}
                            </span>
                            <span className="text-green-600 font-medium">
                              -₫{coupon.discount.toLocaleString()}
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeCoupon(coupon.id)}
                            className="text-gray-400 hover:text-red-500 p-1"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Order Notes */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Order Notes
                  </h3>
                  <Textarea
                    placeholder="Add any special instructions or notes..."
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    className="bg-white resize-none"
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary Card */}
          <div className="lg:col-span-1">
            <Card className="shadow-sm sticky top-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-800">
                  <Receipt className="h-5 w-5 text-blue-600" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Order Summary Details */}
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="font-medium">
                      ₫{subtotal.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Tax (10%):</span>
                    <span className="font-medium">₫{tax.toLocaleString()}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between font-semibold">
                      <span>Total:</span>
                      <span className="text-lg">₫{total.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                {/* Action Buttons */}
                <div className="space-y-3 pt-4">
                  <Button
                    onClick={handleCreateOrder}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 font-medium"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Order
                  </Button>
                  <Button
                    onClick={handleSaveAsDraft}
                    variant="outline"
                    className="w-full py-3 font-medium"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save as Draft
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateOrder;
