import { useState, useEffect, useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import {
  Calendar,
  Clock,
  Users,
  MessageSquare,
  Send,
  FileText,
  Target,
  Eye,
  TestTube,
} from "lucide-react";
import { LayoutContext } from "@/routes/__root";

interface NotificationTemplate {
  id: number;
  templateKey: string;
  type: string;
  titleTemplate: string;
  messageTemplate: string;
  isActive: boolean;
}

interface UserSegment {
  id: string;
  name: string;
  count: number;
  criteria: string;
  userType?: string;
}

interface NotificationForm {
  templateId?: number;
  customTitle: string;
  customMessage: string;
  targetAudience: UserSegment[];
  scheduledDate: string;
  scheduledTime: string;
  sendImmediately: boolean;
  testVariables: Record<string, string>;
}

const SendNotification = () => {
  const { setActiveTab } = useContext(LayoutContext);
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<NotificationTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showTestVariables, setShowTestVariables] = useState(false);
  
  const [formData, setFormData] = useState<NotificationForm>({
    customTitle: "",
    customMessage: "",
    targetAudience: [],
    scheduledDate: "",
    scheduledTime: "",
    sendImmediately: false,
    testVariables: {},
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    setActiveTab("notifications");
    // Mock templates - replace with actual API call
    setTemplates([
      {
        id: 1,
        templateKey: "voucher_created",
        type: "VOUCHER_CREATED",
        titleTemplate: "New Voucher Available: {{.VoucherCode}}",
        messageTemplate: "A new voucher '{{.Title}}' worth {{formatCurrency .DiscountValue}} is now available! Use code {{.VoucherCode}} before {{formatDate .ValidUntil}}.",
        isActive: true,
      },
      {
        id: 2,
        templateKey: "voucher_expiring",
        type: "VOUCHER_EXPIRING",
        titleTemplate: "Voucher Expiring Soon: {{.VoucherCode}}",
        messageTemplate: "Your voucher {{.VoucherCode}} will expire in {{timeUntil .ExpiresAt}}. Don't miss out on your savings!",
        isActive: true,
      },
      {
        id: 3,
        templateKey: "user_welcome",
        type: "USER_WELCOME",
        titleTemplate: "Welcome to Coupon System, {{.Name}}!",
        messageTemplate: "Welcome {{.Name}}! Thank you for joining our coupon system. Start saving money with exclusive deals and vouchers.",
        isActive: true,
      },
    ]);
  }, [setActiveTab]);

  const userSegments: UserSegment[] = [
    { id: "all", name: "All Users", count: 15420, criteria: "All registered users" },
    { id: "premium", name: "Premium Users", count: 2340, criteria: "Users with premium membership", userType: "PREMIUM" },
    { id: "regular", name: "Regular Users", count: 13080, criteria: "Users with regular membership", userType: "REGULAR" },
    { id: "new", name: "New Users", count: 1250, criteria: "Users registered in last 30 days" },
    { id: "active", name: "Active Users", count: 8930, criteria: "Users active in last 7 days" },
  ];

  const getTemplateVariables = (type: string): Record<string, string> => {
    switch (type) {
      case "VOUCHER_CREATED":
        return {
          VoucherCode: "SAVE20",
          Title: "Winter Sale Voucher",
          DiscountValue: "50000",
          ValidUntil: "2024-02-15T23:59:59Z",
        };
      case "VOUCHER_EXPIRING":
        return {
          VoucherCode: "WINTER50",
          ExpiresAt: "2024-01-20T23:59:59Z",
        };
      case "USER_WELCOME":
        return {
          Name: "John Doe",
        };
      case "USER_TYPE_UPGRADE":
        return {
          Name: "John Doe",
          NewType: "Premium",
        };
      default:
        return {};
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id.toString() === templateId);
    if (template) {
      setSelectedTemplate(template);
      setFormData(prev => ({
        ...prev,
        templateId: template.id,
        customTitle: template.titleTemplate,
        customMessage: template.messageTemplate,
        testVariables: getTemplateVariables(template.type),
      }));
    }
  };

  const handleAudienceToggle = (segment: UserSegment) => {
    setFormData(prev => ({
      ...prev,
      targetAudience: prev.targetAudience.find(a => a.id === segment.id)
        ? prev.targetAudience.filter(a => a.id !== segment.id)
        : [...prev.targetAudience, segment],
    }));
  };

  const renderTemplate = (template: string, variables: Record<string, string>): string => {
    let rendered = template;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*\\.${key}\\s*}}`, 'g');
      rendered = rendered.replace(regex, value);
    });
    
    // Handle template functions (mock implementation)
    rendered = rendered.replace(/{{formatCurrency\s+\.(\w+)}}/g, (match, key) => {
      const value = variables[key];
      return value ? `₫${parseInt(value).toLocaleString()}` : match;
    });
    
    rendered = rendered.replace(/{{formatDate\s+\.(\w+)}}/g, (match, key) => {
      const value = variables[key];
      return value ? new Date(value).toLocaleDateString() : match;
    });
    
    rendered = rendered.replace(/{{timeUntil\s+\.(\w+)}}/g, (match, key) => {
      const value = variables[key];
      if (value) {
        const diff = new Date(value).getTime() - new Date().getTime();
        const hours = Math.floor(diff / (1000 * 60 * 60));
        return `${hours} hours`;
      }
      return match;
    });
    
    return rendered;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customTitle.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.customMessage.trim()) {
      newErrors.message = "Message is required";
    }

    if (formData.targetAudience.length === 0) {
      newErrors.audience = "Please select at least one audience segment";
    }

    if (!formData.sendImmediately && !formData.scheduledDate) {
      newErrors.scheduledDate = "Scheduled date is required";
    }

    if (!formData.sendImmediately && !formData.scheduledTime) {
      newErrors.scheduledTime = "Scheduled time is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSendNotification = () => {
    if (validateForm()) {
      console.log("Sending notification:", formData);
      // Implement API call here
      alert("Notification scheduled successfully!");
    }
  };

  const totalRecipients = formData.targetAudience.reduce((sum, segment) => sum + segment.count, 0);

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Send Notification
            </h1>
            <p className="text-gray-600 mt-2">
              Create and schedule notifications for your users
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
              <Eye className="h-4 w-4 mr-2" />
              {showPreview ? "Hide Preview" : "Show Preview"}
            </Button>
            <Button variant="outline" onClick={() => setShowTestVariables(!showTestVariables)}>
              <TestTube className="h-4 w-4 mr-2" />
              Test Variables
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            {/* Template Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span>Template Selection</span>
                </CardTitle>
                <CardDescription>
                  Choose a template or create a custom notification
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="template">Select Template (Optional)</Label>
                    <Select onValueChange={handleTemplateSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a template or create custom" />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id.toString()}>
                            <div className="flex items-center space-x-2">
                              <span>{template.templateKey.replace(/_/g, " ").toUpperCase()}</span>
                              <Badge variant="secondary" className="text-xs">
                                {template.type.replace(/_/g, " ")}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedTemplate && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="text-sm text-blue-800 mb-2">Template Preview:</div>
                      <div className="text-sm font-medium">{selectedTemplate.titleTemplate}</div>
                      <div className="text-xs text-blue-600 mt-1">{selectedTemplate.messageTemplate}</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Message Content */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                  <span>Message Content</span>
                </CardTitle>
                <CardDescription>
                  Customize your notification title and message
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.customTitle}
                    onChange={(e) => setFormData(prev => ({ ...prev, customTitle: e.target.value }))}
                    placeholder="Enter notification title"
                    className={errors.title ? "border-red-500" : ""}
                  />
                  {errors.title && <p className="text-sm text-red-500 mt-1">{errors.title}</p>}
                </div>
                
                <div>
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    value={formData.customMessage}
                    onChange={(e) => setFormData(prev => ({ ...prev, customMessage: e.target.value }))}
                    placeholder="Enter notification message"
                    rows={4}
                    className={errors.message ? "border-red-500" : ""}
                  />
                  {errors.message && <p className="text-sm text-red-500 mt-1">{errors.message}</p>}
                </div>
              </CardContent>
            </Card>

            {/* Audience Targeting */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-purple-600" />
                  <span>Audience Targeting</span>
                </CardTitle>
                <CardDescription>
                  Select who should receive this notification
                </CardDescription>
              </CardHeader>
              <CardContent>
                {errors.audience && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600">{errors.audience}</p>
                  </div>
                )}
                
                <div className="space-y-3">
                  {userSegments.map((segment) => (
                    <div
                      key={segment.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={formData.targetAudience.some(a => a.id === segment.id)}
                          onCheckedChange={() => handleAudienceToggle(segment)}
                        />
                        <div>
                          <p className="font-medium">{segment.name}</p>
                          <p className="text-sm text-gray-500">{segment.criteria}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium">{segment.count.toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                </div>

                {formData.targetAudience.length > 0 && (
                  <div className="mt-4 p-3 bg-green-50 rounded-lg">
                    <p className="text-sm font-medium text-green-800">
                      Total Recipients: {totalRecipients.toLocaleString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Scheduling */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <span>Scheduling</span>
                </CardTitle>
                <CardDescription>
                  Choose when to send the notification
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.sendImmediately}
                    onCheckedChange={(checked) => 
                      setFormData(prev => ({ ...prev, sendImmediately: checked }))
                    }
                  />
                  <Label>Send immediately</Label>
                </div>

                {!formData.sendImmediately && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="date">Date</Label>
                      <Input
                        id="date"
                        type="date"
                        value={formData.scheduledDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, scheduledDate: e.target.value }))}
                        className={errors.scheduledDate ? "border-red-500" : ""}
                      />
                      {errors.scheduledDate && (
                        <p className="text-sm text-red-500 mt-1">{errors.scheduledDate}</p>
                      )}
                    </div>
                    
                    <div>
                      <Label htmlFor="time">Time</Label>
                      <Input
                        id="time"
                        type="time"
                        value={formData.scheduledTime}
                        onChange={(e) => setFormData(prev => ({ ...prev, scheduledTime: e.target.value }))}
                        className={errors.scheduledTime ? "border-red-500" : ""}
                      />
                      {errors.scheduledTime && (
                        <p className="text-sm text-red-500 mt-1">{errors.scheduledTime}</p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          {showPreview && (
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle>Preview</CardTitle>
                  <CardDescription>
                    How your notification will appear
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 border rounded-lg bg-gray-50">
                    <div className="text-sm text-gray-600 mb-2">Title:</div>
                    <div className="font-medium">
                      {formData.customTitle ?
                        renderTemplate(formData.customTitle, formData.testVariables) :
                        "No title"
                      }
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg bg-gray-50">
                    <div className="text-sm text-gray-600 mb-2">Message:</div>
                    <div className="text-sm">
                      {formData.customMessage ?
                        renderTemplate(formData.customMessage, formData.testVariables) :
                        "No message"
                      }
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-600">Recipients:</span>{" "}
                      {totalRecipients.toLocaleString()}
                    </div>
                    <div>
                      <span className="text-gray-600">Schedule:</span>{" "}
                      {formData.sendImmediately
                        ? "Immediate"
                        : formData.scheduledDate && formData.scheduledTime
                          ? `${formData.scheduledDate} at ${formData.scheduledTime}`
                          : "Not set"
                      }
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Test Variables Panel */}
          {showTestVariables && (
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle>Test Variables</CardTitle>
                  <CardDescription>
                    Customize variables for template preview
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedTemplate && Object.keys(formData.testVariables).length > 0 ? (
                    <div className="space-y-3">
                      {Object.entries(formData.testVariables).map(([key, value]) => (
                        <div key={key}>
                          <Label htmlFor={`var-${key}`} className="text-sm">
                            {key}
                          </Label>
                          <Input
                            id={`var-${key}`}
                            value={value}
                            onChange={(e) =>
                              setFormData(prev => ({
                                ...prev,
                                testVariables: {
                                  ...prev.testVariables,
                                  [key]: e.target.value
                                }
                              }))
                            }
                            className="text-sm"
                          />
                        </div>
                      ))}

                      <Separator />

                      <div className="text-xs text-gray-500">
                        <div className="font-medium mb-2">Available Functions:</div>
                        <div>• formatCurrency - Format numbers as currency</div>
                        <div>• formatDate - Format dates</div>
                        <div>• timeUntil - Calculate time until date</div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <TestTube className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Select a template to test variables</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 mt-8">
          <Button variant="outline">
            Save as Draft
          </Button>
          <Button onClick={handleSendNotification} className="min-w-[140px]">
            <Send className="h-4 w-4 mr-2" />
            {formData.sendImmediately ? "Send Now" : "Schedule"}
          </Button>
        </div>
      </main>
    </div>
  );
};

export default SendNotification;
