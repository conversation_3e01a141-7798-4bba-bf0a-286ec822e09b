import { useEffect, useState, useMemo, useContext } from "react";
import { LayoutContext } from "@/routes/__root";
import { Search, Plus, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useVouchersList } from "@/services/vouchers";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  type SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { createVoucherListColumns } from "@/components/vouchers/VoucherListColumns";

function getPageNumbers(current: number, total: number) {
  if (total <= 5) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }
  const pages: (number | string)[] = [1];
  let start = current - 1;
  let end = current + 1;

  if (start <= 2) {
    start = 2;
    end = start + 2;
  }

  if (end >= total - 1) {
    end = total - 1;
    start = end - 2;
  }

  if (start > 2) pages.push("...");
  for (let i = start; i <= end; i++) pages.push(i);
  if (end < total - 1) pages.push("...");
  pages.push(total);
  return pages;
}

export default function Vouchers() {
  const { setActiveTab } = useContext(LayoutContext);

  useEffect(() => {
    setActiveTab("vouchers");
  }, [setActiveTab]);

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All Status");
  const [typeFilter, setTypeFilter] = useState("All Types");
  const [methodFilter, setMethodFilter] = useState("All Methods");

  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [sorting, setSorting] = useState<SortingState>([]);

  const DISCOUNT_TYPE_IDS: Record<string, number | undefined> = {
    Percentage: 1,
    "Fixed Amount": 2,
    "Flat Price": 3,
  };

  const USAGE_METHOD_CODES: Record<string, string | undefined> = {
    "Manual Entry": "MANUAL",
    "Auto-applied": "AUTO",
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("All Status");
    setTypeFilter("All Types");
    setMethodFilter("All Methods");
  };

  const { data, isLoading, isFetching, error } = useVouchersList({
    page: pagination.pageIndex + 1,
    limit: pagination.pageSize,
    search: searchTerm || undefined,
    status: statusFilter !== "All Status" ? statusFilter : undefined,
    discountTypeId: DISCOUNT_TYPE_IDS[typeFilter],
    usageMethod: USAGE_METHOD_CODES[methodFilter],
    sortBy: sorting[0]?.id,
    sortOrder: sorting[0]?.desc ? "desc" : "asc",
  });

  const vouchers = data?.data ?? [];
  const totalPages = Math.ceil((data?.total ?? 0) / (data?.limit ?? 1));

  const columns = useMemo(() => createVoucherListColumns(), []);

  const table = useReactTable({
    data: vouchers,
    columns,
    pageCount: totalPages,
    manualPagination: true,
    state: { sorting, pagination },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const pageNumbers = useMemo(
    () => getPageNumbers(pagination.pageIndex + 1, totalPages),
    [pagination.pageIndex, totalPages],
  );

  const isInitialLoading = isLoading && !data;

  if (error) {
    return <div className="p-6 text-red-600">Failed to load vouchers</div>;
  }

  return (
    <div className="flex-1 bg-white">
      <div className="px-6 py-6">
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Voucher Code
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search by code..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-10"
                  />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Discount Type
                </label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="h-10 w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Types">All Types</SelectItem>
                    <SelectItem value="Percentage">Percentage</SelectItem>
                    <SelectItem value="Fixed Amount">Fixed Amount</SelectItem>
                    <SelectItem value="Flat Price">Flat Price</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Usage Method
                </label>
                <Select value={methodFilter} onValueChange={setMethodFilter}>
                  <SelectTrigger className="h-10 w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Methods">All Methods</SelectItem>
                    <SelectItem value="Manual Entry">Manual Entry</SelectItem>
                    <SelectItem value="Auto-applied">Auto-applied</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="h-10 w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Status">All Status</SelectItem>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Expired">Expired</SelectItem>
                    <SelectItem value="Upcoming">Upcoming</SelectItem>
                  </SelectContent>
                </Select>
              </div>{" "}
            </div>
            <div className="flex items-center space-x-4 mt-4">
              <Button className="bg-blue-600 hover:bg-blue-700" type="button">
                <Search className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
              <Button variant="outline" onClick={clearFilters} type="button">
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>All Vouchers</CardTitle>
            <Button className="bg-blue-600 hover:bg-blue-700" type="button">
              <Plus className="h-4 w-4 mr-2" />
              Create New Voucher
            </Button>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-3 px-4 font-medium text-gray-500 uppercase text-xs tracking-wider"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {isInitialLoading || isFetching
                    ? Array.from({ length: pagination.pageSize }).map(
                        (_, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50">
                            {table.getVisibleLeafColumns().map((column) => (
                              <td key={column.id} className="py-4 px-4">
                                <Skeleton className="h-4 w-full" />
                              </td>
                            ))}
                          </tr>
                        ),
                      )
                    : table.getRowModel().rows.map((row) => (
                        <tr key={row.id} className="hover:bg-gray-50">
                          {row.getVisibleCells().map((cell) => (
                            <td key={cell.id} className="py-4 px-4">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                </tbody>
              </table>
              {(isInitialLoading || isFetching) && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/50">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mt-6">
              <span className="text-sm text-gray-500">
                Page {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  <ChevronLeft className="w-4 h-4" /> Previous
                </Button>
                {pageNumbers.map((p, idx) =>
                  typeof p === "number" ? (
                    <Button
                      key={p}
                      variant={
                        p - 1 === table.getState().pagination.pageIndex
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      className="px-3 py-1"
                      onClick={() => table.setPageIndex(p - 1)}
                    >
                      {p}
                    </Button>
                  ) : (
                    <span key={`ellipsis-${idx}`} className="px-2">
                      ...
                    </span>
                  ),
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Next <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
