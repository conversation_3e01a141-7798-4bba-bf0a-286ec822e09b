import { useState, useEffect, useContext, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Plus, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useOrdersList } from "@/services/orders";
import { LayoutContext } from "@/routes/__root";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { createOrderListColumns } from "@/components/orders/OrderListColumn";

function getPageNumbers(current: number, total: number) {
  if (total <= 5) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }
  const pages: (number | string)[] = [1];
  let start = current - 1;
  let end = current + 1;

  if (start <= 2) {
    start = 2;
    end = start + 2;
  }

  if (end >= total - 1) {
    end = total - 1;
    start = end - 2;
  }

  if (start > 2) pages.push("...");
  for (let i = start; i <= end; i++) pages.push(i);
  if (end < total - 1) pages.push("...");
  pages.push(total);
  return pages;
}

export default function Orders() {
  const { setActiveTab } = useContext(LayoutContext);
  useEffect(() => {
    setActiveTab("orders");
  }, [setActiveTab]);

  const [showCreateForm, setShowCreateForm] = useState(false);

  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  const { data, isLoading, isFetching, error } = useOrdersList({
    page: pagination.pageIndex + 1,
    limit: pagination.pageSize,
  });
  const orders = data?.data ?? [];
  const totalPages = data?.totalPages ?? 1;

  const columns = useMemo(() => createOrderListColumns(), []);
  const table = useReactTable({
    data: orders,
    columns,
    pageCount: totalPages,
    manualPagination: true,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const pageNumbers = useMemo(
    () => getPageNumbers(pagination.pageIndex + 1, totalPages),
    [pagination.pageIndex, totalPages],
  );

  const isInitialLoading = isLoading && !data;

  if (error) {
    return <div className="p-6 text-red-600">Failed to load orders</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-semibold flex items-center gap-8">
                All Orders
                <div className="flex items-center space-x-4">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm">
                    Filter
                  </Button>
                </div>
              </CardTitle>
              <Button
                onClick={() => setShowCreateForm(!showCreateForm)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Order
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-3 px-4 font-medium text-gray-500 uppercase text-xs tracking-wider"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {isInitialLoading || isFetching
                    ? Array.from({ length: pagination.pageSize }).map(
                        (_, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50">
                            {table.getVisibleLeafColumns().map((column) => (
                              <td key={column.id} className="py-4 px-4">
                                <Skeleton className="h-4 w-full" />
                              </td>
                            ))}
                          </tr>
                        ),
                      )
                    : table.getRowModel().rows.map((row) => (
                        <tr key={row.id} className="hover:bg-gray-50">
                          {row.getVisibleCells().map((cell) => (
                            <td key={cell.id} className="py-4 px-4">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                </tbody>
              </table>
              {(isInitialLoading || isFetching) && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/50">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mt-6">
              <span className="text-sm text-gray-500">
                Page {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  <ChevronLeft className="w-4 h-4" /> Previous
                </Button>
                {pageNumbers.map((p, idx) =>
                  typeof p === "number" ? (
                    <Button
                      key={p}
                      variant={
                        p - 1 === table.getState().pagination.pageIndex
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      className="px-3 py-1"
                      onClick={() => table.setPageIndex(p - 1)}
                    >
                      {p}
                    </Button>
                  ) : (
                    <span key={`ellipsis-${idx}`} className="px-2">
                      ...
                    </span>
                  ),
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Next <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
