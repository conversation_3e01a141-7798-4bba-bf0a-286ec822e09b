import { useState, useEffect, useContext, useMemo } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Plus,
  FileText,
  Save,
} from "lucide-react";
import { LayoutContext } from "@/routes/__root";
import {
  createNotificationTemplatesColumns,
  type NotificationTemplateItem
} from "@/components/notifications/NotificationTemplatesColumns";

interface NotificationTemplate {
  id: number;
  templateKey: string;
  type: string;
  titleTemplate: string;
  messageTemplate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const NotificationTemplates = () => {
  const { setActiveTab } = useContext(LayoutContext);
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);


  const [formData, setFormData] = useState({
    templateKey: "",
    type: "",
    titleTemplate: "",
    messageTemplate: "",
    isActive: true,
  });

  useEffect(() => {
    setActiveTab("notifications");
    // Mock data - replace with actual API call
    setTemplates([
      {
        id: 1,
        templateKey: "voucher_created",
        type: "VOUCHER_CREATED",
        titleTemplate: "New Voucher Available: {{.VoucherCode}}",
        messageTemplate: "A new voucher '{{.Title}}' worth {{formatCurrency .DiscountValue}} is now available! Use code {{.VoucherCode}} before {{formatDate .ValidUntil}}.",
        isActive: true,
        createdAt: "2024-01-10T10:00:00Z",
        updatedAt: "2024-01-10T10:00:00Z",
      },
      {
        id: 2,
        templateKey: "voucher_expiring",
        type: "VOUCHER_EXPIRING",
        titleTemplate: "Voucher Expiring Soon: {{.VoucherCode}}",
        messageTemplate: "Your voucher {{.VoucherCode}} will expire in {{timeUntil .ExpiresAt}}. Don't miss out on your savings!",
        isActive: true,
        createdAt: "2024-01-10T10:00:00Z",
        updatedAt: "2024-01-10T10:00:00Z",
      },
      {
        id: 3,
        templateKey: "user_welcome",
        type: "USER_WELCOME",
        titleTemplate: "Welcome to Coupon System, {{.Name}}!",
        messageTemplate: "Welcome {{.Name}}! Thank you for joining our coupon system. Start saving money with exclusive deals and vouchers.",
        isActive: true,
        createdAt: "2024-01-10T10:00:00Z",
        updatedAt: "2024-01-10T10:00:00Z",
      },
    ]);
  }, [setActiveTab]);

  // Transform templates to match the column interface
  const tableData: NotificationTemplateItem[] = useMemo(() =>
    templates.map(template => ({
      id: template.id,
      name: template.templateKey,
      type: template.type,
      status: template.isActive ? "ACTIVE" : "INACTIVE" as "ACTIVE" | "INACTIVE" | "DRAFT",
      subject: template.titleTemplate,
      description: template.messageTemplate,
      usageCount: Math.floor(Math.random() * 100), // Mock usage count
      lastUsed: template.isActive ? "2024-01-15T12:00:00Z" : undefined,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    })), [templates]);

  const columns = useMemo(() => createNotificationTemplatesColumns(), []);

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const notificationTypes = [
    { value: "VOUCHER_CREATED", label: "Voucher Created" },
    { value: "VOUCHER_EXPIRING", label: "Voucher Expiring" },
    { value: "VOUCHER_USED", label: "Voucher Used" },
    { value: "ORDER_CREATED", label: "Order Created" },
    { value: "VOUCHER_APPLIED", label: "Voucher Applied" },
    { value: "USER_WELCOME", label: "User Welcome" },
    { value: "USER_TYPE_UPGRADE", label: "User Type Upgrade" },
  ];

  const getTypeVariables = (type: string): Record<string, string> => {
    switch (type) {
      case "VOUCHER_CREATED":
        return {
          VoucherCode: "SAVE20",
          Title: "Winter Sale Voucher",
          DiscountValue: "50000",
          ValidUntil: "2024-02-15T23:59:59Z",
        };
      case "VOUCHER_EXPIRING":
        return {
          VoucherCode: "WINTER50",
          ExpiresAt: "2024-01-20T23:59:59Z",
        };
      case "USER_WELCOME":
        return {
          Name: "John Doe",
        };
      case "USER_TYPE_UPGRADE":
        return {
          Name: "John Doe",
          NewType: "Premium",
        };
      default:
        return {};
    }
  };

  const handleCreateTemplate = () => {
    // Mock create - replace with actual API call
    const newTemplate: NotificationTemplate = {
      id: templates.length + 1,
      ...formData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setTemplates([...templates, newTemplate]);
    setIsCreateDialogOpen(false);
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      templateKey: "",
      type: "",
      titleTemplate: "",
      messageTemplate: "",
      isActive: true,
    });
  };

  const renderTemplate = (template: string, variables: Record<string, string>): string => {
    let rendered = template;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*\\.${key}\\s*}}`, 'g');
      rendered = rendered.replace(regex, value);
    });
    return rendered;
  };

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Notification Templates
            </h1>
            <p className="text-gray-600 mt-2">
              Manage and customize notification templates
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Template</DialogTitle>
                <DialogDescription>
                  Create a new notification template with dynamic variables
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Form */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="templateKey">Template Key</Label>
                    <Input
                      id="templateKey"
                      value={formData.templateKey}
                      onChange={(e) => setFormData({...formData, templateKey: e.target.value})}
                      placeholder="e.g., voucher_created"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="type">Notification Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select notification type" />
                      </SelectTrigger>
                      <SelectContent>
                        {notificationTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="titleTemplate">Title Template</Label>
                    <Input
                      id="titleTemplate"
                      value={formData.titleTemplate}
                      onChange={(e) => setFormData({...formData, titleTemplate: e.target.value})}
                      placeholder="e.g., New Voucher: {{.VoucherCode}}"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="messageTemplate">Message Template</Label>
                    <Textarea
                      id="messageTemplate"
                      value={formData.messageTemplate}
                      onChange={(e) => setFormData({...formData, messageTemplate: e.target.value})}
                      placeholder="e.g., Your voucher {{.VoucherCode}} is ready!"
                      rows={4}
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => setFormData({...formData, isActive: checked})}
                    />
                    <Label htmlFor="isActive">Active</Label>
                  </div>
                </div>
                
                {/* Preview */}
                <div className="space-y-4">
                  <div>
                    <Label>Preview</Label>
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm">
                          {formData.titleTemplate ? 
                            renderTemplate(formData.titleTemplate, getTypeVariables(formData.type)) : 
                            "Title preview will appear here"
                          }
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600">
                          {formData.messageTemplate ? 
                            renderTemplate(formData.messageTemplate, getTypeVariables(formData.type)) : 
                            "Message preview will appear here"
                          }
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {formData.type && (
                    <div>
                      <Label>Available Variables</Label>
                      <Card>
                        <CardContent className="pt-4">
                          <div className="space-y-2">
                            {Object.entries(getTypeVariables(formData.type)).map(([key, value]) => (
                              <div key={key} className="flex justify-between text-sm">
                                <code className="bg-gray-100 px-2 py-1 rounded">
                                  {`{{.${key}}}`}
                                </code>
                                <span className="text-gray-600">{value}</span>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateTemplate}>
                  <Save className="h-4 w-4 mr-2" />
                  Create Template
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Templates Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <span>Templates ({templates.length})</span>
            </CardTitle>
            <CardDescription>
              Manage your notification templates and their configurations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-3 px-4 font-medium text-gray-500 uppercase text-xs tracking-wider"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="py-4 px-4">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default NotificationTemplates;
