import { useState, useEffect, useContext, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Download,
  ChevronLeft,
  ChevronRight,
  Loader2,
  RotateCcw,
  XCircle,
  Trash2,
} from "lucide-react";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  type SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { LayoutContext } from "@/routes/__root";
import {
  createNotificationHistoryColumns,
  type NotificationHistoryItem
} from "@/components/notifications/NotificationHistoryColumns";

function getPageNumbers(current: number, total: number) {
  if (total <= 5) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }
  const pages: (number | string)[] = [1];
  let start = current - 1;
  let end = current + 1;

  if (start <= 2) {
    start = 2;
    end = start + 2;
  }

  if (end >= total - 1) {
    end = total - 1;
    start = end - 2;
  }

  if (start > 2) pages.push("...");
  for (let i = start; i <= end; i++) pages.push(i);
  if (end < total - 1) pages.push("...");
  pages.push(total);
  return pages;
}

const NotificationHistory = () => {
  const { setActiveTab } = useContext(LayoutContext);
  const [notifications, setNotifications] = useState<NotificationHistoryItem[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All Status");
  const [typeFilter, setTypeFilter] = useState("All Types");

  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 20 });
  const [sorting, setSorting] = useState<SortingState>([{ id: "createdAt", desc: true }]);

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("All Status");
    setTypeFilter("All Types");
  };

  useEffect(() => {
    setActiveTab("notifications");
    // Mock data - replace with actual API call
    setNotifications([
      {
        id: 1,
        userId: 1,
        type: "VOUCHER_CREATED",
        title: "New Voucher Available: SAVE20",
        message: "A new voucher 'Winter Sale' worth ₫50,000 is now available! Use code SAVE20 before 2024-02-15.",
        status: "SENT",
        sentAt: "2024-01-15T14:30:00Z",
        readAt: "2024-01-15T15:45:00Z",
        createdAt: "2024-01-15T14:25:00Z",
        user: { id: 1, name: "John Doe", email: "<EMAIL>" },
      },
      {
        id: 2,
        userId: 2,
        type: "VOUCHER_EXPIRING",
        title: "Voucher Expiring Soon: WINTER50",
        message: "Your voucher WINTER50 will expire in 24 hours. Don't miss out on your savings!",
        status: "PENDING",
        scheduledAt: "2024-01-16T09:00:00Z",
        createdAt: "2024-01-15T12:00:00Z",
        user: { id: 2, name: "Jane Smith", email: "<EMAIL>" },
      },
      {
        id: 3,
        userId: 3,
        type: "USER_WELCOME",
        title: "Welcome to Coupon System, Alice!",
        message: "Welcome Alice! Thank you for joining our coupon system. Start saving money with exclusive deals.",
        status: "SENT",
        sentAt: "2024-01-15T10:15:00Z",
        createdAt: "2024-01-15T10:10:00Z",
        user: { id: 3, name: "Alice Johnson", email: "<EMAIL>" },
      },
      {
        id: 4,
        userId: 1,
        type: "VOUCHER_USED",
        title: "Voucher Used Successfully",
        message: "Your voucher SAVE20 has been applied to order #1234. You saved ₫50,000!",
        status: "READ",
        sentAt: "2024-01-14T16:20:00Z",
        readAt: "2024-01-14T16:25:00Z",
        createdAt: "2024-01-14T16:18:00Z",
        user: { id: 1, name: "John Doe", email: "<EMAIL>" },
      },
      {
        id: 5,
        userId: 4,
        type: "ORDER_CONFIRMATION",
        title: "Order Confirmation #1235",
        message: "Your order has been created successfully. Total amount: ₫150,000",
        status: "FAILED",
        createdAt: "2024-01-14T14:30:00Z",
        user: { id: 4, name: "Bob Wilson", email: "<EMAIL>" },
      },
    ]);
  }, [setActiveTab]);

  const notificationTypes = [
    { value: "VOUCHER_CREATED", label: "Voucher Created" },
    { value: "VOUCHER_EXPIRING", label: "Voucher Expiring" },
    { value: "VOUCHER_USED", label: "Voucher Used" },
    { value: "ORDER_CONFIRMATION", label: "Order Confirmation" },
    { value: "VOUCHER_APPLIED", label: "Voucher Applied" },
    { value: "VOUCHER_FAILED", label: "Voucher Failed" },
    { value: "USER_WELCOME", label: "User Welcome" },
    { value: "USER_TYPE_UPGRADE", label: "User Type Upgrade" },
  ];

  const statusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "SENT", label: "Sent" },
    { value: "FAILED", label: "Failed" },
    { value: "READ", label: "Read" },
    { value: "CANCELLED", label: "Cancelled" },
  ];

  // Filter and search logic
  const filteredNotifications = useMemo(() => {
    return notifications.filter(notification => {
      const matchesSearch = searchTerm === "" ||
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.user?.email.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === "All Status" || notification.status === statusFilter;
      const matchesType = typeFilter === "All Types" || notification.type === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [notifications, searchTerm, statusFilter, typeFilter]);

  const totalPages = Math.ceil(filteredNotifications.length / pagination.pageSize);

  const handleSelectAll = () => {
    if (selectedIds.length === filteredNotifications.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(filteredNotifications.map(n => n.id));
    }
  };

  const handleSelectOne = (id: number) => {
    setSelectedIds(current =>
      current.includes(id)
        ? current.filter(selectedId => selectedId !== id)
        : [...current, id]
    );
  };

  const columns = useMemo(() =>
    createNotificationHistoryColumns(
      selectedIds,
      handleSelectOne,
      handleSelectAll,
      filteredNotifications.length
    ),
    [selectedIds, filteredNotifications.length]
  );

  const table = useReactTable({
    data: filteredNotifications,
    columns,
    pageCount: totalPages,
    manualPagination: true,
    state: { sorting, pagination },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const pageNumbers = useMemo(
    () => getPageNumbers(pagination.pageIndex + 1, totalPages),
    [pagination.pageIndex, totalPages],
  );

  const handleBulkAction = (action: string) => {
    if (selectedIds.length > 0) {
      console.log(`Bulk action: ${action}`, selectedIds);
      // Implement bulk actions here
      setSelectedIds([]);
    }
  };

  const isInitialLoading = false; // Replace with actual loading state

  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Notification History
            </h1>
            <p className="text-gray-600 mt-2">
              View and manage all notification records
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-10"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="h-10 w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Status">All Status</SelectItem>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Type
                </label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="h-10 w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Types">All Types</SelectItem>
                    {notificationTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button className="bg-blue-600 hover:bg-blue-700 mr-2" type="button">
                  <Search className="h-4 w-4 mr-2" />
                  Apply Filters
                </Button>
                <Button variant="outline" onClick={clearFilters} type="button">
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedIds.length > 0 && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {selectedIds.length} notification{selectedIds.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <Button size="sm" onClick={() => handleBulkAction("resend")}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Resend
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkAction("cancel")}>
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button size="sm" variant="destructive" onClick={() => handleBulkAction("delete")}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Notifications Table */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>All Notifications ({filteredNotifications.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-3 px-4 font-medium text-gray-500 uppercase text-xs tracking-wider"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {isInitialLoading
                    ? Array.from({ length: pagination.pageSize }).map(
                        (_, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50">
                            {table.getVisibleLeafColumns().map((column) => (
                              <td key={column.id} className="py-4 px-4">
                                <Skeleton className="h-4 w-full" />
                              </td>
                            ))}
                          </tr>
                        ),
                      )
                    : table.getRowModel().rows.map((row) => (
                        <tr key={row.id} className="hover:bg-gray-50">
                          {row.getVisibleCells().map((cell) => (
                            <td key={cell.id} className="py-4 px-4">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                </tbody>
              </table>
              {isInitialLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/50">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mt-6">
              <span className="text-sm text-gray-500">
                Page {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  <ChevronLeft className="w-4 h-4" /> Previous
                </Button>
                {pageNumbers.map((p, idx) =>
                  typeof p === "number" ? (
                    <Button
                      key={p}
                      variant={
                        p - 1 === table.getState().pagination.pageIndex
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      className="px-3 py-1"
                      onClick={() => table.setPageIndex(p - 1)}
                    >
                      {p}
                    </Button>
                  ) : (
                    <span key={`ellipsis-${idx}`} className="px-2">
                      ...
                    </span>
                  ),
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Next <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default NotificationHistory;
