import { useEffect, useContext, useMemo } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import {
  Bell,
  Send,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  Plus,
  Settings,
  BarChart3,
} from "lucide-react";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { LayoutContext } from "@/routes/__root";
import {
  createNotificationManagementColumns,
  type NotificationManagementItem
} from "@/components/notifications/NotificationManagementColumns";

const NotificationManagement = () => {
  const { setActiveTab } = useContext(LayoutContext);
  
  useEffect(() => {
    setActiveTab("notifications");
  }, [setActiveTab]);

  // Mock data - replace with actual API calls
  const stats = {
    totalNotifications: 1247,
    deliveryRate: 94.2,
    activeTemplates: 12,
    engagementRate: 68.5,
  };

  const recentNotifications: NotificationManagementItem[] = [
    {
      id: 1,
      title: "New Voucher Available: SAVE20",
      type: "VOUCHER_CREATED",
      status: "SENT",
      recipient: "All Premium Users",
      sentAt: "2024-01-15T14:30:00Z",
      readCount: 245,
      totalSent: 320,
      createdAt: "2024-01-15T14:25:00Z",
    },
    {
      id: 2,
      title: "Voucher Expiring Soon: WINTER50",
      type: "VOUCHER_EXPIRING",
      status: "PENDING",
      recipient: "Eligible Users",
      scheduledAt: "2024-01-16T09:00:00Z",
      readCount: 0,
      totalSent: 0,
      createdAt: "2024-01-15T12:00:00Z",
    },
    {
      id: 3,
      title: "Welcome to Coupon System",
      type: "USER_WELCOME",
      status: "SENT",
      recipient: "New Users",
      sentAt: "2024-01-15T12:15:00Z",
      readCount: 89,
      totalSent: 156,
      createdAt: "2024-01-15T12:10:00Z",
    },
  ];

  const columns = useMemo(() => createNotificationManagementColumns(), []);

  const table = useReactTable({
    data: recentNotifications,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });



  return (
    <div className="min-h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Notification Management
            </h1>
            <p className="text-gray-600 mt-2">
              Manage templates, send notifications, and track engagement
            </p>
          </div>
          <div className="flex space-x-3">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Template
            </Button>
            <Button variant="outline">
              <Send className="h-4 w-4 mr-2" />
              Send Notification
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Notifications
              </CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalNotifications.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +12% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Delivery Rate
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.deliveryRate}%</div>
              <p className="text-xs text-muted-foreground">
                +2.1% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Templates
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeTemplates}</div>
              <p className="text-xs text-muted-foreground">
                +3 new this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Engagement Rate
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.engagementRate}%</div>
              <p className="text-xs text-muted-foreground">
                +5.2% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions and Recent Notifications */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <span>Quick Actions</span>
              </CardTitle>
              <CardDescription>
                Common notification management tasks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                Manage Templates
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Clock className="h-4 w-4 mr-2" />
                Scheduled Notifications
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Bell className="h-4 w-4 mr-2" />
                Notification History
              </Button>
            </CardContent>
          </Card>

          {/* Recent Notifications */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-green-600" />
                  <span>Recent Notifications</span>
                </span>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </CardTitle>
              <CardDescription>
                Latest notification activity and status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            className="text-left py-3 px-4 font-medium text-gray-500 uppercase text-xs tracking-wider"
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext(),
                                )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {table.getRowModel().rows.map((row) => (
                      <tr key={row.id} className="hover:bg-gray-50">
                        {row.getVisibleCells().map((cell) => (
                          <td key={cell.id} className="py-4 px-4">
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default NotificationManagement;
