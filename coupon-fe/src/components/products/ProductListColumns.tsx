import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { ColumnDef } from "@tanstack/react-table";
import type { Product } from "@/types/products";

export function createProductListColumns(
  addToCart: (product: Product) => void,
): ColumnDef<Product, unknown>[] {
  return [
    {
      id: "name",
      header: "PRODUCT",
      accessorFn: (row) => row.name,
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <img
            src={row.original.imageUrl}
            alt={row.original.name}
            className="w-10 h-10 object-cover rounded"
          />
          <div>
            <p className="font-medium">{row.original.name}</p>
            <p className="text-xs text-gray-500">{row.original.brand}</p>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "price",
      header: "PRICE",
      cell: (info) => (
        <span className="font-medium">
          ₫{(info.getValue<number>() ?? 0).toLocaleString()}
        </span>
      ),
    },
    {
      accessorKey: "stockQuantity",
      header: "STOCK",
      cell: (info) => {
        const value = info.getValue<number>() ?? 0;
        return (
          <Badge
            className={
              value > 0
                ? "bg-green-100 text-green-800"
                : "bg-gray-100 text-gray-600"
            }
            variant="outline"
          >
            {value}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: "ACTIONS",
      cell: ({ row }) => (
        <Button
          size="sm"
          className="bg-blue-500 hover:bg-blue-600"
          onClick={() => addToCart(row.original)}
        >
          Add to Cart
        </Button>
      ),
    },
  ];
}
