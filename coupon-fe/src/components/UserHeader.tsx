import { CreditCardIcon, ChevronDown, LogOut } from "lucide-react";
import { useUser } from "@/services/user";
import { useLogout } from "@/services/auth";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import NotificationBell from "@/components/NotificationBell";
import { useListNotifications, useUpdateNotificationStatus } from "@/services/notifications";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useState } from "react";

function initials(name?: string) {
  if (!name) return "";
  const parts = name.split(" ");
  const first = parts[0]?.charAt(0) ?? "";
  const last = parts[1]?.charAt(0) ?? "";
  return `${first}${last}`.toUpperCase();
}

export function UserHeader() {
  const user = useUser();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const notifications = useListNotifications();
  const updateNotificationStatus = useUpdateNotificationStatus();
  const logout = useLogout();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const handleNotificationClick = (_notification: any) => {
    // Handle notification click - could navigate to relevant page
    // Removed console.log as requested
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await updateNotificationStatus.mutateAsync({
        id: parseInt(notificationId),
        body: {
          notificationId: parseInt(notificationId),
          status: "READ"
        }
      });
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ["get", "/api/notifications"] });
    } catch (error) {
      // Removed console.error as requested
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!notifications.data?.notifications) return;

    const unreadNotifications = notifications.data.notifications.filter(n => n.status !== "READ");

    try {
      await Promise.all(
        unreadNotifications.map(notification =>
          updateNotificationStatus.mutateAsync({
            id: notification.id,
            body: {
              notificationId: notification.id,
              status: "READ"
            }
          })
        )
      );
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ["get", "/api/notifications"] });
    } catch (error) {
      // Removed console.error as requested
    }
  };

  const handleClearAll = () => {
    // For now, just invalidate the query to refresh
    queryClient.invalidateQueries({ queryKey: ["get", "/api/notifications"] });
  };

  const handleLogout = async () => {
    try {
      await logout.mutateAsync({});
      // Clear all cached data
      queryClient.clear();
      // Redirect to login page
      navigate({ to: '/login' });
    } catch (error) {
      // Removed console.error as requested
    }
    setIsUserMenuOpen(false);
  };

  return (
    <header className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 z-10">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <CreditCardIcon className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-lg font-semibold text-gray-900">ZaloPay</h1>
        </div>
        <div className="flex items-center gap-4">
          <NotificationBell
            notifications={notifications.data?.notifications || []}
            onNotificationClick={handleNotificationClick}
            onMarkAsRead={handleMarkAsRead}
            onMarkAllAsRead={handleMarkAllAsRead}
            onClearAll={handleClearAll}
          />
          <Popover open={isUserMenuOpen} onOpenChange={setIsUserMenuOpen}>
            <PopoverTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-2 h-auto p-2">
                {user.isPending ? (
                  <Skeleton className="h-5 w-20" />
                ) : (
                  <>
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        {initials(user.data?.name)}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {user.data?.name ?? ""}
                    </span>
                  </>
                )}
                <ChevronDown className="w-4 h-4 text-gray-600" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-2" align="end">
              <Button
                variant="ghost"
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleLogout}
                disabled={logout.isPending}
              >
                <LogOut className="w-4 h-4 mr-2" />
                {logout.isPending ? "Logging out..." : "Logout"}
              </Button>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}
