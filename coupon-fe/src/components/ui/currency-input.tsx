import * as React from "react";
import { Input } from "./input";

interface CurrencyInputProps
  extends Omit<React.ComponentProps<typeof Input>, "onChange" | "value"> {
  value?: number | null;
  onChange?: (value?: number) => void;
}

function CurrencyInput({ value, onChange, ...props }: CurrencyInputProps) {
  const [display, setDisplay] = React.useState<string>(() => {
    if (value === undefined || value === null) return "";
    return value.toLocaleString("en-US");
  });

  React.useEffect(() => {
    if (value === undefined || value === null) {
      setDisplay("");
    } else {
      setDisplay(value.toLocaleString("en-US"));
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value.replace(/,/g, "");
    const num = raw === "" ? undefined : Number(raw);
    if (onChange) onChange(num);
    if (raw === "") {
      setDisplay("");
    } else {
      setDisplay(Number(raw).toLocaleString("en-US"));
    }
  };

  return (
    <Input
      {...props}
      value={display}
      onChange={handleChange}
      inputMode="numeric"
    />
  );
}

export { CurrencyInput };
