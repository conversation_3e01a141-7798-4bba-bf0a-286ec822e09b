import React from 'react';
import { 
  Gift, 
  ShoppingCart, 
  User, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Star,
  CreditCard
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import type { NotificationType, NotificationStatus } from '@/types/notifications';

interface NotificationItemProps {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  status: NotificationStatus;
  onClick?: () => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  id,
  type,
  title,
  message,
  timestamp,
  status,
  onClick
}) => {
  const getNotificationConfig = (type: NotificationType) => {
    switch (type) {
      case 'VOUCHER_CREATED':
        return {
          icon: Gift,
          iconColor: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          badgeColor: 'bg-green-100 text-green-800',
          label: 'Voucher'
        };
      case 'VOUCHER_EXPIRING':
        return {
          icon: AlertTriangle,
          iconColor: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          badgeColor: 'bg-orange-100 text-orange-800',
          label: 'Expiring'
        };
      case 'VOUCHER_USED':
        return {
          icon: CheckCircle,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          badgeColor: 'bg-blue-100 text-blue-800',
          label: 'Used'
        };
      case 'VOUCHER_APPLIED':
        return {
          icon: CreditCard,
          iconColor: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          badgeColor: 'bg-purple-100 text-purple-800',
          label: 'Applied'
        };
      case 'VOUCHER_FAILED':
        return {
          icon: XCircle,
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          badgeColor: 'bg-red-100 text-red-800',
          label: 'Failed'
        };
      case 'ORDER_CONFIRMATION':
        return {
          icon: ShoppingCart,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          badgeColor: 'bg-blue-100 text-blue-800',
          label: 'Order'
        };
      case 'USER_WELCOME':
        return {
          icon: User,
          iconColor: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          badgeColor: 'bg-purple-100 text-purple-800',
          label: 'Welcome'
        };
      case 'USER_TYPE_UPGRADE':
        return {
          icon: Star,
          iconColor: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          badgeColor: 'bg-yellow-100 text-yellow-800',
          label: 'Upgrade'
        };
      default:
        return {
          icon: Gift,
          iconColor: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          badgeColor: 'bg-gray-100 text-gray-800',
          label: 'Notification'
        };
    }
  };

  const config = getNotificationConfig(type);
  const IconComponent = config.icon;
  const isUnread = status !== 'READ';
  const isPending = status === 'PENDING';
  const isFailed = status === 'FAILED';

  return (
    <Card 
      className={`p-4 transition-all duration-200 hover:shadow-md cursor-pointer ${
        isUnread ? 'bg-background border-l-4 ' + config.borderColor : 'bg-muted/30'
      } ${isFailed ? 'opacity-75' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-start space-x-3">
        <div className={`flex-shrink-0 w-10 h-10 rounded-full ${config.bgColor} flex items-center justify-center`}>
          <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className={`text-sm font-medium ${isUnread ? 'text-foreground' : 'text-muted-foreground'}`}>
              {title}
            </h4>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className={`text-xs ${config.badgeColor}`}>
                {config.label}
              </Badge>
              {isPending && (
                <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
                  Pending
                </Badge>
              )}
              {isFailed && (
                <Badge variant="outline" className="text-xs text-red-600 border-red-200">
                  Failed
                </Badge>
              )}
              {isUnread && !isPending && !isFailed && (
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
              )}
            </div>
          </div>
          
          <p className={`text-sm ${isUnread ? 'text-muted-foreground' : 'text-muted-foreground/70'} line-clamp-2`}>
            {message}
          </p>
          
          <div className="flex items-center mt-2 text-xs text-muted-foreground">
            <Clock className="w-3 h-3 mr-1" />
            {timestamp}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NotificationItem;
