import React, { useState } from "react";
import { Calculator, CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

export const OrderCalculator: React.FC = () => {
  const [orderData, setOrderData] = useState({
    amount: "",
    timestamp: "",
    couponCode: "",
  });

  const [result, setResult] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleCalculate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCalculating(true);

    setTimeout(() => {
      const amount = parseFloat(orderData.amount);

      const mockCoupons = [
        {
          code: "SUMMER2023",
          type: "Percentage",
          value: 20,
          valid: true,
          description: "20% off",
        },
        {
          code: "WELCOME50",
          type: "Fixed Amount",
          value: 50000,
          valid: true,
          description: "50,000 VND off",
        },
        {
          code: "EXPIRED",
          type: "Percentage",
          value: 30,
          valid: false,
          description: "Expired coupon",
        },
      ];

      let appliedCoupon = null;
      let discount = 0;
      let isValid = false;

      if (orderData.couponCode) {
        appliedCoupon = mockCoupons.find(
          (c) => c.code === orderData.couponCode.toUpperCase(),
        );
        if (appliedCoupon && appliedCoupon.valid) {
          isValid = true;
          if (appliedCoupon.type === "Percentage") {
            discount = (amount * appliedCoupon.value) / 100;
          } else {
            discount = appliedCoupon.value;
          }
        }
      } else {
        const validCoupons = mockCoupons.filter((c) => c.valid);
        let bestDiscount = 0;

        validCoupons.forEach((coupon) => {
          let potentialDiscount = 0;
          if (coupon.type === "Percentage") {
            potentialDiscount = (amount * coupon.value) / 100;
          } else {
            potentialDiscount = coupon.value;
          }

          if (potentialDiscount > bestDiscount) {
            bestDiscount = potentialDiscount;
            appliedCoupon = coupon;
            discount = potentialDiscount;
            isValid = true;
          }
        });
      }

      const finalAmount = Math.max(0, amount - discount);

      setResult({
        originalAmount: amount,
        discount,
        finalAmount,
        appliedCoupon,
        isValid,
        error:
          appliedCoupon && !appliedCoupon.valid
            ? "Coupon is expired or invalid"
            : null,
      });

      setIsCalculating(false);
    }, 1000);
  };

  const handleInputChange = (field: string, value: string) => {
    setOrderData((prev) => ({ ...prev, [field]: value }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Order Calculator</h1>
        <p className="text-gray-600 mt-1">
          Calculate discount amounts for customer orders
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-2 mb-6">
            <Calculator className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold">Order Information</h2>
          </div>

          <form onSubmit={handleCalculate} className="space-y-4">
            <div>
              <Label htmlFor="amount">Order Amount (VND) *</Label>
              <Input
                id="amount"
                type="number"
                step="1000"
                value={orderData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                placeholder="500000"
                required
              />
            </div>

            <div>
              <Label htmlFor="timestamp">Order Timestamp *</Label>
              <Input
                id="timestamp"
                type="datetime-local"
                value={orderData.timestamp}
                onChange={(e) => handleInputChange("timestamp", e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="couponCode">Coupon Code (Optional)</Label>
              <Input
                id="couponCode"
                value={orderData.couponCode}
                onChange={(e) =>
                  handleInputChange("couponCode", e.target.value)
                }
                placeholder="Enter coupon code or leave empty for auto-apply"
              />
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to automatically apply the best available coupon
              </p>
            </div>

            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700"
              disabled={isCalculating}
            >
              {isCalculating ? "Calculating..." : "Calculate Discount"}
            </Button>
          </form>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-6">Calculation Results</h2>

          {!result ? (
            <div className="text-center py-8 text-gray-500">
              <Calculator className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>Enter order details and click calculate to see results</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                {result.isValid ? (
                  <>
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Coupon Applied Successfully
                    </Badge>
                  </>
                ) : (
                  <>
                    <XCircle className="w-5 h-5 text-red-600" />
                    <Badge className="bg-red-100 text-red-800 border-red-200">
                      {result.error || "No Valid Coupon Found"}
                    </Badge>
                  </>
                )}
              </div>

              {result.appliedCoupon && result.isValid && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-2">
                    Applied Coupon
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p>
                      <span className="font-medium">Code:</span>{" "}
                      {result.appliedCoupon.code}
                    </p>
                    <p>
                      <span className="font-medium">Description:</span>{" "}
                      {result.appliedCoupon.description}
                    </p>
                    <p>
                      <span className="font-medium">Type:</span>{" "}
                      {result.appliedCoupon.type}
                    </p>
                  </div>
                </div>
              )}

              <div className="space-y-3 border-t border-gray-200 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Original Amount:</span>
                  <span className="font-medium">
                    {formatCurrency(result.originalAmount)}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Discount:</span>
                  <span className="font-medium text-green-600">
                    -{formatCurrency(result.discount)}
                  </span>
                </div>

                <div className="flex justify-between items-center text-lg font-semibold border-t border-gray-200 pt-3">
                  <span>Final Amount:</span>
                  <span className="text-blue-600">
                    {formatCurrency(result.finalAmount)}
                  </span>
                </div>

                {result.discount > 0 && (
                  <div className="text-sm text-green-600 text-center bg-green-50 p-2 rounded">
                    You saved {formatCurrency(result.discount)}!
                  </div>
                )}
              </div>
            </div>
          )}
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Available Test Coupons</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium">SUMMER2023</h4>
            <p className="text-sm text-gray-600">20% discount</p>
            <Badge className="bg-green-100 text-green-800 border-green-200 mt-2">
              Active
            </Badge>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium">WELCOME50</h4>
            <p className="text-sm text-gray-600">50,000 VND off</p>
            <Badge className="bg-green-100 text-green-800 border-green-200 mt-2">
              Active
            </Badge>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium">EXPIRED</h4>
            <p className="text-sm text-gray-600">30% discount</p>
            <Badge className="bg-red-100 text-red-800 border-red-200 mt-2">
              Expired
            </Badge>
          </div>
        </div>
      </Card>
    </div>
  );
};
