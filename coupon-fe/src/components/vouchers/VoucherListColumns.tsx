import { Badge } from "@/components/ui/badge";
import { Edit, MoreHorizontal, Eye } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { useNavigate } from "@tanstack/react-router";
import type { VoucherListItem } from "@/types/vouchers";

export const getStatusColor = (status: string) => {
  switch (status) {
    case "ACTIVE":
      return "bg-green-100 text-green-800 hover:bg-green-100";
    case "EXPIRED":
      return "bg-red-100 text-red-800 hover:bg-red-100";
    case "UPCOMING":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const getDiscountTypeColor = (typeName: string) => {
  switch (typeName?.toLowerCase()) {
    case "percentage":
      return "bg-green-100 text-green-800 hover:bg-green-100";
    case "fixed amount":
      return "bg-blue-100 text-blue-800 hover:bg-blue-100";
    case "flat price":
      return "bg-purple-100 text-purple-800 hover:bg-purple-100";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
  }
};

export const getUsageMethodColor = (method: string) => {
  switch (method?.toUpperCase()) {
    case "MANUAL":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
    case "AUTO":
      return "bg-indigo-100 text-indigo-800 hover:bg-indigo-100";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
  }
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(amount);
};

export const formatDiscountValue = (value: number, typeCode: string) => {
  if (typeCode === "PERCENT") {
    return `${value}%`;
  } else if (typeCode === "FIXED" || typeCode === "FLAT") {
    return `${value.toLocaleString()}₫`;
  }
  return value.toString();
};

export const formatDateTime = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export function createVoucherListColumns(): ColumnDef<
  VoucherListItem,
  unknown
>[] {
  return [
    {
      accessorKey: "voucherCode",
      header: "VOUCHER CODE",
      cell: (info) => (
        <span className="font-medium text-blue-600 hover:text-blue-800">
          {info.getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "title",
      header: "TITLE",
      cell: (info) => (
        <Badge variant="outline" className="capitalize">
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      id: "discountType",
      header: "DISCOUNT TYPE",
      accessorFn: (row) => row.discountTypeName,
      cell: (info) => (
        <Badge
          className={`${getDiscountTypeColor(String(info.getValue()))} px-2 py-1 rounded-full text-xs font-medium`}
        >
          {String(info.getValue())}
        </Badge>
      ),
    },
    {
      id: "discountValue",
      header: "DISCOUNT VALUE",
      accessorFn: (row) => ({
        value: row.discountValue,
        typeCode: row.discountTypeCode,
      }),
      cell: (info) => {
        const data = info.getValue() as { value: number; typeCode: string };
        return (
          <Badge variant="secondary" className="font-medium">
            {formatDiscountValue(data.value, data.typeCode)}
          </Badge>
        );
      },
    },
    {
      id: "usageMethod",
      header: "USAGE METHOD",
      accessorFn: (row) => row.usageMethod,
      cell: (info) => (
        <Badge
          className={`${getUsageMethodColor(info.getValue() as string)} px-2 py-1 rounded-full text-xs font-medium`}
        >
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      id: "validUntil",
      header: "EXPIRY DATE",
      accessorFn: (row) => row.validUntil,
      cell: (info) => (
        <span className="text-gray-600 text-sm">
          {formatDateTime(info.getValue() as string)}
        </span>
      ),
    },
    {
      accessorKey: "status",
      header: "STATUS",
      cell: (info) => (
        <Badge
          className={`${getStatusColor(info.getValue() as string)} px-2 py-1 rounded-full text-xs font-medium`}
        >
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      id: "actions",
      header: "ACTIONS",
      cell: ({ row }) => {
        return <RowActions row={row} />;
      },
    },
  ];
}

function RowActions({ row }: { row: Row<VoucherListItem> }) {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setOpen((o) => !o)}
        className="p-1 text-gray-600 hover:bg-gray-100 rounded"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>
      {open && (
        <div className="absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10 text-sm">
          <button
            onClick={() => {
              navigate({ to: `/vouchers/${row.original.id}` });
              setOpen(false);
            }}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Eye className="h-4 w-4 mr-2" /> View Details
          </button>
          <button
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
            onClick={() => {
              navigate({ to: `/vouchers/${row.original.id}/edit` });
              setOpen(false);
            }}
          >
            <Edit className="h-4 w-4 mr-2" /> Edit Coupon
          </button>
          <button className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50">
            Duplicate
          </button>
          <button className="flex w-full items-center px-2 py-1.5 text-red-600 hover:bg-gray-50">
            Delete
          </button>
        </div>
      )}
    </div>
  );
}
