import React, { useState } from "react";
import { ArrowLef<PERSON>, Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CouponFormProps {
  coupon?: any;
  onCancel: () => void;
}

export const CouponForm: React.FC<CouponFormProps> = ({ coupon, onCancel }) => {
  const [formData, setFormData] = useState({
    coupon_code: coupon?.coupon_code || "",
    title: coupon?.title || "",
    description: coupon?.description || "",
    discountType: coupon?.discount_type?.type_name || "Percentage",
    discountValue: coupon?.discount_value || "",
    usageMethod: coupon?.usage_method?.method_name || "Manual Entry",
    valid_from: coupon?.valid_from?.slice(0, 10) || "",
    valid_until: coupon?.valid_until?.slice(0, 10) || "",
  });

  const isEdit = !!coupon;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    onCancel();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={onCancel} className="p-2">
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEdit ? "Edit Coupon" : "Create New Coupon"}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEdit
              ? "Update coupon information"
              : "Add a new discount coupon to your system"}
          </p>
        </div>
      </div>

      <Card className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="coupon_code">Coupon Code *</Label>
              <Input
                id="coupon_code"
                value={formData.coupon_code}
                onChange={(e) =>
                  handleInputChange("coupon_code", e.target.value)
                }
                placeholder="e.g., SUMMER2023"
                disabled={isEdit}
                className={isEdit ? "bg-gray-50" : ""}
                required
              />
              {isEdit && (
                <p className="text-xs text-gray-500 mt-1">
                  Coupon code cannot be changed
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="e.g., Summer Discount"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Describe your coupon offer..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label>Discount Type *</Label>
              <Select
                value={formData.discountType}
                onValueChange={(value) =>
                  handleInputChange("discountType", value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Percentage">Percentage</SelectItem>
                  <SelectItem value="Fixed Amount">Fixed Amount</SelectItem>
                  <SelectItem value="Flat Price">Flat Price</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Discount Value */}
            <div>
              <Label htmlFor="discountValue">
                Discount Value *
                {formData.discountType === "Percentage" && " (%)"}
                {formData.discountType === "Fixed Amount" && " (VND)"}
                {formData.discountType === "Flat Price" && " (VND)"}
              </Label>
              <Input
                id="discountValue"
                type="number"
                value={formData.discountValue}
                onChange={(e) =>
                  handleInputChange("discountValue", e.target.value)
                }
                placeholder={
                  formData.discountType === "Percentage"
                    ? "20"
                    : formData.discountType === "Fixed Amount"
                      ? "50000"
                      : "99000"
                }
                required
              />
            </div>
          </div>

          <div>
            <Label>Usage Method *</Label>
            <Select
              value={formData.usageMethod}
              onValueChange={(value) => handleInputChange("usageMethod", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Manual Entry">Manual Entry</SelectItem>
                <SelectItem value="Auto-applied">Auto-applied</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              {formData.usageMethod === "Manual Entry"
                ? "Users must enter the coupon code to apply discount"
                : "Best available coupon will be automatically applied"}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="valid_from">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.valid_from}
                onChange={(e) =>
                  handleInputChange("valid_from", e.target.value)
                }
                required
              />
            </div>

            <div>
              <Label htmlFor="valid_until">End Date *</Label>
              <Input
                id="valid_until"
                type="date"
                value={formData.valid_until}
                onChange={(e) =>
                  handleInputChange("valid_until", e.target.value)
                }
                required
              />
            </div>
          </div>

          <div className="flex items-center space-x-4 pt-6 border-t border-gray-200">
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              <Save className="w-4 h-4 mr-2" />
              {isEdit ? "Update Coupon" : "Create Coupon"}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};
