import React, { useState, useRef, useEffect } from 'react';
import { Bell, X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import NotificationItem from '@/components/NotificationItem';
import type { NotificationResponse } from '@/types/notifications';

interface NotificationBellProps {
  notifications?: NotificationResponse[];
  onNotificationClick?: (notification: NotificationResponse) => void;
  onMarkAsRead?: (notificationId: string) => void;
  onMarkAllAsRead?: () => void;
  onClearAll?: () => void;
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  notifications = [],
  onNotificationClick = () => {},
  onMarkAsRead = () => {},
  onMarkAllAsRead = () => {},
  onClearAll = () => {},
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [notificationList, setNotificationList] = useState(notifications);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const unreadCount = notificationList.filter(n => n.status !== 'READ').length;

  useEffect(() => {
    setNotificationList(notifications);
  }, [notifications]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleMarkAsRead = (notificationId: string) => {
    setNotificationList(prev =>
      prev.map(n =>
        n.id.toString() === notificationId ? { ...n, status: 'READ' as const } : n
      )
    );
    onMarkAsRead(notificationId);
  };

  const handleMarkAllAsRead = () => {
    setNotificationList(prev =>
      prev.map(n => ({ ...n, status: 'READ' as const }))
    );
    onMarkAllAsRead();
  };

  const handleClearAll = () => {
    setNotificationList([]);
    onClearAll();
  };

  const formatTimestamp = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className="relative">
      <Button
        ref={buttonRef}
        variant="ghost"
        size="icon"
        className="relative h-10 w-10 rounded-full hover:bg-muted"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Bell className="h-5 w-5 text-foreground" />
        {unreadCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs font-medium"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <Card
          ref={dropdownRef}
          className="absolute right-0 top-12 w-80 bg-background border border-border shadow-lg z-50"
        >
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">
                Notifications
              </h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-xs text-muted-foreground hover:text-foreground"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                  className="h-6 w-6"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-muted-foreground mt-1">
                You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </p>
            )}
          </div>

          <ScrollArea className="max-h-96">
            {notificationList.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No notifications</p>
              </div>
            ) : (
              <div className="space-y-2 p-2">
                {notificationList.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    id={notification.id.toString()}
                    type={notification.type}
                    title={notification.title}
                    message={notification.message}
                    timestamp={formatTimestamp(notification.createdAt)}
                    status={notification.status}
                    onClick={() => {
                      onNotificationClick(notification);
                      if (notification.status !== 'READ') {
                        handleMarkAsRead(notification.id.toString());
                      }
                    }}
                  />
                ))}
              </div>
            )}
          </ScrollArea>

          {notificationList.length > 0 && (
            <>
              <Separator />
              <div className="p-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="w-full text-sm text-muted-foreground hover:text-foreground"
                >
                  Clear all notifications
                </Button>
              </div>
            </>
          )}
        </Card>
      )}
    </div>
  );
};

export default NotificationBell;
