import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  <PERSON>, 
  Edit, 
  Trash2, 
  <PERSON><PERSON>, 
  MoreHorizontal,
  Bell, 
  FileText, 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle,
  Play,
  Pause
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { useNavigate } from "@tanstack/react-router";

// Interface for notification template items
export interface NotificationTemplateItem {
  id: number;
  name: string;
  type: string;
  status: "ACTIVE" | "INACTIVE" | "DRAFT";
  subject: string;
  description: string;
  usageCount: number;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export const getStatusColor = (status: string) => {
  switch (status) {
    case "ACTIVE":
      return "bg-green-100 text-green-800 hover:bg-green-100";
    case "INACTIVE":
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    case "DRAFT":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
  }
};

export const getTypeIcon = (type: string) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (type) {
    case "VOUCHER_CREATED":
      return <FileText {...iconProps} className="h-4 w-4 text-blue-600" />;
    case "VOUCHER_EXPIRING":
      return <Clock {...iconProps} className="h-4 w-4 text-orange-600" />;
    case "VOUCHER_USED":
      return <CheckCircle {...iconProps} className="h-4 w-4 text-green-600" />;
    case "ORDER_CONFIRMATION":
      return <Bell {...iconProps} className="h-4 w-4 text-purple-600" />;
    case "VOUCHER_APPLIED":
      return <CheckCircle {...iconProps} className="h-4 w-4 text-indigo-600" />;
    case "VOUCHER_FAILED":
      return <XCircle {...iconProps} className="h-4 w-4 text-red-600" />;
    case "USER_WELCOME":
      return <Users {...iconProps} className="h-4 w-4 text-emerald-600" />;
    case "USER_TYPE_UPGRADE":
      return <Users {...iconProps} className="h-4 w-4 text-yellow-600" />;
    default:
      return <Bell {...iconProps} className="h-4 w-4 text-gray-600" />;
  }
};

export const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return "-";
  return new Date(timestamp).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export const formatRelativeTime = (timestamp: string) => {
  if (!timestamp) return "-";
  const now = new Date();
  const date = new Date(timestamp);
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  return `${Math.floor(diffInDays / 30)} months ago`;
};

export function createNotificationTemplatesColumns(): ColumnDef<
  NotificationTemplateItem,
  unknown
>[] {
  return [
    {
      id: "type",
      header: "TYPE",
      accessorFn: (row) => row.type,
      cell: (info) => getTypeIcon(info.getValue() as string),
      enableSorting: false,
    },
    {
      accessorKey: "name",
      header: "TEMPLATE NAME",
      cell: (info) => (
        <div>
          <div className="font-medium text-blue-600 hover:text-blue-800">
            {info.getValue() as string}
          </div>
          <div className="text-sm text-gray-500 truncate max-w-xs">
            {info.row.original.subject}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "STATUS",
      cell: (info) => (
        <Badge
          className={`${getStatusColor(info.getValue() as string)} px-2 py-1 rounded-full text-xs font-medium`}
        >
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      accessorKey: "usageCount",
      header: "USAGE COUNT",
      cell: (info) => (
        <div className="text-sm">
          <div className="font-medium">{info.getValue() as number}</div>
          <div className="text-gray-500">times used</div>
        </div>
      ),
    },
    {
      accessorKey: "lastUsed",
      header: "LAST USED",
      cell: (info) => {
        const lastUsed = info.getValue() as string;
        return (
          <div className="text-sm">
            <div className="font-medium">
              {lastUsed ? formatRelativeTime(lastUsed) : "Never"}
            </div>
            {lastUsed && (
              <div className="text-gray-500">
                {formatTimestamp(lastUsed)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "updatedAt",
      header: "LAST MODIFIED",
      cell: (info) => (
        <span className="text-sm text-gray-500">
          {formatTimestamp(info.getValue() as string)}
        </span>
      ),
    },
    {
      id: "actions",
      header: "ACTIONS",
      cell: ({ row }) => {
        return <RowActions row={row} />;
      },
      enableSorting: false,
    },
  ];
}

function RowActions({ row }: { row: Row<NotificationTemplateItem> }) {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  const handleAction = (action: string) => {
    console.log(`Action: ${action}`, row.original);
    setOpen(false);
    
    switch (action) {
      case "view":
        // Navigate to template preview
        break;
      case "edit":
        // Navigate to edit template
        break;
      case "duplicate":
        // Duplicate template
        break;
      case "activate":
        // Activate template
        break;
      case "deactivate":
        // Deactivate template
        break;
      case "delete":
        // Delete template
        break;
    }
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setOpen((o) => !o)}
        className="p-1 text-gray-600 hover:bg-gray-100 rounded"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>
      {open && (
        <div className="absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10 text-sm">
          <button
            onClick={() => handleAction("view")}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Eye className="h-4 w-4 mr-2" /> Preview
          </button>
          <button
            onClick={() => handleAction("edit")}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Edit className="h-4 w-4 mr-2" /> Edit
          </button>
          <button
            onClick={() => handleAction("duplicate")}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Copy className="h-4 w-4 mr-2" /> Duplicate
          </button>
          {row.original.status === "INACTIVE" ? (
            <button
              onClick={() => handleAction("activate")}
              className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
            >
              <Play className="h-4 w-4 mr-2" /> Activate
            </button>
          ) : (
            <button
              onClick={() => handleAction("deactivate")}
              className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
            >
              <Pause className="h-4 w-4 mr-2" /> Deactivate
            </button>
          )}
          <button 
            onClick={() => handleAction("delete")}
            className="flex w-full items-center px-2 py-1.5 text-red-600 hover:bg-gray-50"
          >
            <Trash2 className="h-4 w-4 mr-2" /> Delete
          </button>
        </div>
      )}
    </div>
  );
}
