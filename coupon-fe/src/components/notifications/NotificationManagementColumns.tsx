import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Eye, 
  Edit, 
  Trash2, 
  Send, 
  Pause, 
  Play,
  MoreHorizontal,
  Bell, 
  FileText, 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle 
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import { useNavigate } from "@tanstack/react-router";

// Interface for notification management items
export interface NotificationManagementItem {
  id: number;
  title: string;
  type: string;
  status: "SENT" | "PENDING" | "FAILED" | "CANCELLED";
  recipient: string;
  sentAt?: string;
  scheduledAt?: string;
  readCount: number;
  totalSent: number;
  createdAt: string;
}

export const getStatusColor = (status: string) => {
  switch (status) {
    case "SENT":
      return "bg-green-100 text-green-800 hover:bg-green-100";
    case "PENDING":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
    case "FAILED":
      return "bg-red-100 text-red-800 hover:bg-red-100";
    case "CANCELLED":
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
  }
};

export const getTypeIcon = (type: string) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (type) {
    case "VOUCHER_CREATED":
      return <FileText {...iconProps} className="h-4 w-4 text-blue-600" />;
    case "VOUCHER_EXPIRING":
      return <Clock {...iconProps} className="h-4 w-4 text-orange-600" />;
    case "VOUCHER_USED":
      return <CheckCircle {...iconProps} className="h-4 w-4 text-green-600" />;
    case "ORDER_CONFIRMATION":
      return <Bell {...iconProps} className="h-4 w-4 text-purple-600" />;
    case "VOUCHER_APPLIED":
      return <CheckCircle {...iconProps} className="h-4 w-4 text-indigo-600" />;
    case "VOUCHER_FAILED":
      return <XCircle {...iconProps} className="h-4 w-4 text-red-600" />;
    case "USER_WELCOME":
      return <Users {...iconProps} className="h-4 w-4 text-emerald-600" />;
    case "USER_TYPE_UPGRADE":
      return <Users {...iconProps} className="h-4 w-4 text-yellow-600" />;
    default:
      return <Bell {...iconProps} className="h-4 w-4 text-gray-600" />;
  }
};

export const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return "-";
  return new Date(timestamp).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export const formatEngagementRate = (readCount: number, totalSent: number) => {
  if (totalSent === 0) return "0%";
  return `${((readCount / totalSent) * 100).toFixed(1)}%`;
};

export function createNotificationManagementColumns(): ColumnDef<
  NotificationManagementItem,
  unknown
>[] {
  return [
    {
      id: "type",
      header: "TYPE",
      accessorFn: (row) => row.type,
      cell: (info) => getTypeIcon(info.getValue() as string),
      enableSorting: false,
    },
    {
      accessorKey: "title",
      header: "TITLE",
      cell: (info) => (
        <span className="font-medium text-blue-600 hover:text-blue-800">
          {info.getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "recipient",
      header: "RECIPIENT",
      cell: (info) => (
        <Badge variant="outline" className="capitalize">
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      accessorKey: "status",
      header: "STATUS",
      cell: (info) => (
        <Badge
          className={`${getStatusColor(info.getValue() as string)} px-2 py-1 rounded-full text-xs font-medium`}
        >
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      id: "sentScheduled",
      header: "SENT/SCHEDULED",
      accessorFn: (row) => row.sentAt || row.scheduledAt,
      cell: ({ row }) => (
        <span className="text-sm text-gray-600">
          {row.original.sentAt ? 
            formatTimestamp(row.original.sentAt) : 
            row.original.scheduledAt ? 
              `Scheduled: ${formatTimestamp(row.original.scheduledAt)}` : 
              "-"
          }
        </span>
      ),
    },
    {
      id: "engagement",
      header: "ENGAGEMENT",
      accessorFn: (row) => ({ readCount: row.readCount, totalSent: row.totalSent }),
      cell: (info) => {
        const data = info.getValue() as { readCount: number; totalSent: number };
        return (
          <div className="text-sm">
            <div className="font-medium">
              {formatEngagementRate(data.readCount, data.totalSent)}
            </div>
            <div className="text-gray-500">
              {data.readCount}/{data.totalSent}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "CREATED",
      cell: (info) => (
        <span className="text-sm text-gray-500">
          {formatTimestamp(info.getValue() as string)}
        </span>
      ),
    },
    {
      id: "actions",
      header: "ACTIONS",
      cell: ({ row }) => {
        return <RowActions row={row} />;
      },
      enableSorting: false,
    },
  ];
}

function RowActions({ row }: { row: Row<NotificationManagementItem> }) {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  const handleAction = (action: string) => {
    console.log(`Action: ${action}`, row.original);
    setOpen(false);
    
    switch (action) {
      case "view":
        // Navigate to notification details
        break;
      case "edit":
        // Navigate to edit notification
        break;
      case "send":
        // Send notification
        break;
      case "pause":
        // Pause notification
        break;
      case "resume":
        // Resume notification
        break;
      case "delete":
        // Delete notification
        break;
    }
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setOpen((o) => !o)}
        className="p-1 text-gray-600 hover:bg-gray-100 rounded"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>
      {open && (
        <div className="absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10 text-sm">
          <button
            onClick={() => handleAction("view")}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Eye className="h-4 w-4 mr-2" /> View Details
          </button>
          <button
            onClick={() => handleAction("edit")}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Edit className="h-4 w-4 mr-2" /> Edit
          </button>
          {row.original.status === "PENDING" && (
            <button
              onClick={() => handleAction("send")}
              className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
            >
              <Send className="h-4 w-4 mr-2" /> Send Now
            </button>
          )}
          {row.original.status === "PENDING" && (
            <button
              onClick={() => handleAction("pause")}
              className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
            >
              <Pause className="h-4 w-4 mr-2" /> Pause
            </button>
          )}
          {row.original.status === "CANCELLED" && (
            <button
              onClick={() => handleAction("resume")}
              className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
            >
              <Play className="h-4 w-4 mr-2" /> Resume
            </button>
          )}
          <button 
            onClick={() => handleAction("delete")}
            className="flex w-full items-center px-2 py-1.5 text-red-600 hover:bg-gray-50"
          >
            <Trash2 className="h-4 w-4 mr-2" /> Delete
          </button>
        </div>
      )}
    </div>
  );
}
