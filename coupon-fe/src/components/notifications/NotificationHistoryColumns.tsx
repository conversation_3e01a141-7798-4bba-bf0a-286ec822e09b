import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Eye, 
  Trash2, 
  RotateCcw, 
  Bell, 
  FileText, 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertCircle 
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { type ColumnDef, type Row } from "@tanstack/react-table";
import type { NotificationResponse } from "@/types/notifications";

// Extended interface for notification history with user info
export interface NotificationHistoryItem extends NotificationResponse {
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

export const getStatusColor = (status: string) => {
  switch (status) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
    case "SENT":
      return "bg-green-100 text-green-800 hover:bg-green-100";
    case "FAILED":
      return "bg-red-100 text-red-800 hover:bg-red-100";
    case "READ":
      return "bg-blue-100 text-blue-800 hover:bg-blue-100";
    case "CANCELLED":
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100";
  }
};

export const getTypeIcon = (type: string) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (type) {
    case "VOUCHER_CREATED":
      return <FileText {...iconProps} className="h-4 w-4 text-blue-600" />;
    case "VOUCHER_EXPIRING":
      return <Clock {...iconProps} className="h-4 w-4 text-orange-600" />;
    case "VOUCHER_USED":
      return <CheckCircle {...iconProps} className="h-4 w-4 text-green-600" />;
    case "ORDER_CONFIRMATION":
      return <Bell {...iconProps} className="h-4 w-4 text-purple-600" />;
    case "VOUCHER_APPLIED":
      return <CheckCircle {...iconProps} className="h-4 w-4 text-indigo-600" />;
    case "VOUCHER_FAILED":
      return <XCircle {...iconProps} className="h-4 w-4 text-red-600" />;
    case "USER_WELCOME":
      return <Users {...iconProps} className="h-4 w-4 text-emerald-600" />;
    case "USER_TYPE_UPGRADE":
      return <Users {...iconProps} className="h-4 w-4 text-yellow-600" />;
    default:
      return <Bell {...iconProps} className="h-4 w-4 text-gray-600" />;
  }
};

export const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return "-";
  return new Date(timestamp).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export function createNotificationHistoryColumns(
  selectedIds: number[],
  onSelectOne: (id: number) => void,
  onSelectAll: () => void,
  totalItems: number
): ColumnDef<NotificationHistoryItem, unknown>[] {
  return [
    {
      id: "select",
      header: () => (
        <Checkbox
          checked={selectedIds.length === totalItems && totalItems > 0}
          onCheckedChange={onSelectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedIds.includes(row.original.id)}
          onCheckedChange={() => onSelectOne(row.original.id)}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "type",
      header: "TYPE",
      accessorFn: (row) => row.type,
      cell: (info) => getTypeIcon(info.getValue() as string),
      enableSorting: false,
    },
    {
      accessorKey: "title",
      header: "TITLE",
      cell: ({ row }) => (
        <div>
          <div className="font-medium text-sm">{row.original.title}</div>
          <div className="text-xs text-gray-500 truncate max-w-xs">
            {row.original.message}
          </div>
        </div>
      ),
    },
    {
      id: "recipient",
      header: "RECIPIENT",
      accessorFn: (row) => row.user?.name || "Unknown",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.user?.name || "Unknown"}</div>
          <div className="text-gray-500">{row.original.user?.email || "-"}</div>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "STATUS",
      cell: (info) => (
        <Badge
          className={`${getStatusColor(info.getValue() as string)} px-2 py-1 rounded-full text-xs font-medium`}
        >
          {info.getValue() as string}
        </Badge>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "CREATED",
      cell: (info) => (
        <span className="text-sm text-gray-500">
          {formatTimestamp(info.getValue() as string)}
        </span>
      ),
    },
    {
      id: "sentScheduled",
      header: "SENT/SCHEDULED",
      accessorFn: (row) => row.sentAt || row.scheduledAt,
      cell: ({ row }) => (
        <span className="text-sm text-gray-500">
          {row.original.sentAt ? 
            formatTimestamp(row.original.sentAt) : 
            row.original.scheduledAt ? 
              `Scheduled: ${formatTimestamp(row.original.scheduledAt)}` : 
              "-"
          }
        </span>
      ),
    },
    {
      id: "actions",
      header: "ACTIONS",
      cell: ({ row }) => {
        return <RowActions row={row} />;
      },
      enableSorting: false,
    },
  ];
}

function RowActions({ row }: { row: Row<NotificationHistoryItem> }) {
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  const handleAction = (action: string) => {
    console.log(`Action: ${action}`, row.original);
    setOpen(false);
  };

  return (
    <div className="flex justify-end space-x-2">
      <Button variant="ghost" size="sm" onClick={() => handleAction("view")}>
        <Eye className="h-4 w-4" />
      </Button>
      {row.original.status === "FAILED" && (
        <Button variant="ghost" size="sm" onClick={() => handleAction("resend")}>
          <RotateCcw className="h-4 w-4" />
        </Button>
      )}
      <Button 
        variant="ghost" 
        size="sm" 
        className="text-red-600 hover:text-red-700"
        onClick={() => handleAction("delete")}
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}
