import { Badge } from "@/components/ui/badge";
import { Eye, Trash2, MoreHorizontal } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { type ColumnDef } from "@tanstack/react-table";
import type { Order } from "@/types/orders";

export function createOrderListColumns(): ColumnDef<Order, unknown>[] {
  return [
    {
      accessorKey: "id",
      header: "ORDER ID",
      cell: (info) => (
        <span className="font-medium">{info.getValue<number>()}</span>
      ),
    },
    {
      accessorKey: "orderAmount",
      header: "AMOUNT",
      cell: (info) => `${(info.getValue<number>() ?? 0).toLocaleString()} VND`,
    },
    {
      accessorKey: "couponCode",
      header: "COUPON USED",
      cell: (info) => info.getValue<string>() || "-",
    },
    {
      accessorKey: "discountAmount",
      header: "DISCOUNT",
      cell: (info) => {
        const value = info.getValue<number>() ?? 0;
        return (
          <span className={value > 0 ? "text-red-600" : ""}>
            {value > 0 ? `-${value.toLocaleString()}` : "0"} VND
          </span>
        );
      },
    },
    {
      accessorKey: "finalAmount",
      header: "FINAL AMOUNT",
      cell: (info) => (
        <span className="text-green-600 font-semibold">
          {(info.getValue<number>() ?? 0).toLocaleString()} VND
        </span>
      ),
    },
    {
      accessorKey: "orderTimestamp",
      header: "ORDER TIME",
      cell: (info) => String(info.getValue()),
    },
    {
      accessorKey: "calculationStatus",
      header: "STATUS",
      cell: (info) => (
        <Badge
          variant={
            info.getValue<string>() === "Completed" ? "default" : "secondary"
          }
          className={
            info.getValue<string>() === "Completed"
              ? "bg-green-100 text-green-800"
              : "bg-yellow-100 text-yellow-800"
          }
        >
          {info.getValue<string>()}
        </Badge>
      ),
    },
    {
      id: "actions",
      header: "ACTIONS",
      cell: () => <RowActions />,
    },
  ];
}

function RowActions() {
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setOpen((o) => !o)}
        className="p-1 text-gray-600 hover:bg-gray-100 rounded"
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>
      {open && (
        <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10 text-sm">
          <button
            onClick={() => {
              setOpen(false);
            }}
            className="flex w-full items-center px-2 py-1.5 hover:bg-gray-50"
          >
            <Eye className="h-4 w-4 mr-2" /> View
          </button>
          <button
            className="flex w-full items-center px-2 py-1.5 text-red-600 hover:bg-gray-50"
            onClick={() => {
              setOpen(false);
            }}
          >
            <Trash2 className="h-4 w-4 mr-2" /> Delete
          </button>
        </div>
      )}
    </div>
  );
}
