{"name": "coupon-fe", "private": true, "type": "module", "scripts": {"dev": "vite --port 4000", "start": "vite --port 4000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "api:pull": "openapi-typescript http://localhost:8080/docs.json -o src/openapi-spec.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-query": "^5.81.2", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/react-table": "^8.21.3", "@tanstack/router-plugin": "^1.121.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.522.0", "openapi-fetch": "^0.14.0", "openapi-react-query": "^0.5.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.81.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.29.0", "jsdom": "^26.0.0", "openapi-typescript": "^7.8.0", "typescript": "^5.8.3", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}, "overrides": {"@tanstack/react-router": "^1.121.2"}}