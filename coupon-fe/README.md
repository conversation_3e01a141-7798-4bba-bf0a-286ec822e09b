# Coupon Frontend Application

A modern React-based frontend application for the coupon microservice system, built with TypeScript, Vite, and a comprehensive UI component library.

## 🎯 Overview

The Coupon Frontend provides:

- **User Interface**: Modern, responsive web interface for the coupon system
- **Authentication**: Cookie-based authentication with JWT tokens
- **Product Catalog**: Browse and search products with filtering
- **Voucher Management**: View, create, and manage vouchers
- **Order Processing**: Create orders with voucher application
- **User Dashboard**: Personal dashboard with order history and notifications
- **Admin Panel**: Administrative interface for system management

### Key Features

- **Modern React Stack**: Built with React 18, TypeScript, and Vite
- **Component Library**: Shadcn/ui components with Tailwind CSS
- **Routing**: File-based routing with TanStack Router
- **State Management**: TanStack Query for server state management
- **Authentication**: Secure cookie-based authentication
- **Responsive Design**: Mobile-first responsive design
- **Testing**: Comprehensive testing with Vitest
- **Performance**: Optimized build with code splitting and lazy loading

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Browser   │───▶│  Frontend App    │───▶│   API Gateway   │
│                 │    │                  │    │                 │
│ - React UI      │    │ - React/TS       │    │ - REST APIs     │
│ - User Actions  │    │ - TanStack Query │    │ - Authentication│
│ - Responsive    │    │ - TanStack Router│    │ - HTTP/JSON     │
└─────────────────┘    │ - Shadcn/ui      │    └─────────────────┘
                       │ - Tailwind CSS   │
                       │ - Cookie Auth    │
                       └──────────────────┘
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** 18+ or **Bun** runtime
- **Package Manager**: npm, yarn, pnpm, or bun
- **API Gateway**: Running coupon-api-gateway service

### Installation

1. **Clone and install dependencies**
   ```bash
   # Using Bun (recommended)
   bun install

   # Or using npm
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start development server**
   ```bash
   # Using Bun
   bunx --bun run start

   # Or using npm
   npm run dev
   ```

4. **Access the application**
   - Development: `http://localhost:3000`
   - API Gateway: `http://localhost:8080`

### Production Build

```bash
# Build for production
bunx --bun run build

# Preview production build
bunx --bun run preview

# Serve production build
bunx --bun run serve
```

## 🧪 Testing

### Test Framework

This project uses [Vitest](https://vitest.dev/) for testing with React Testing Library.

```bash
# Run tests
bunx --bun run test

# Run tests in watch mode
bunx --bun run test:watch

# Run tests with coverage
bunx --bun run test:coverage

# Run tests in UI mode
bunx --bun run test:ui
```

### Test Structure

```
src/
├── __tests__/              # Global test utilities
├── components/
│   ├── ui/
│   │   └── button.test.tsx # Component tests
│   └── forms/
│       └── login-form.test.tsx
├── pages/
│   └── login.test.tsx      # Page tests
└── utils/
    └── api.test.ts         # Utility tests
```

## 🎨 Styling

### Tailwind CSS

This project uses [Tailwind CSS](https://tailwindcss.com/) for styling with a custom design system.

```bash
# Tailwind configuration
tailwind.config.js

# Global styles
src/styles/globals.css

# Component styles
src/components/ui/
```

### Design System

- **Colors**: Custom color palette with dark/light mode support
- **Typography**: Consistent font scales and weights
- **Spacing**: Standardized spacing system
- **Components**: Reusable UI components with variants

## 🧩 Component Library

### Shadcn/ui Components

Add components using the latest version of [Shadcn](https://ui.shadcn.com/).

```bash
# Add individual components
pnpx shadcn@latest add button
pnpx shadcn@latest add input
pnpx shadcn@latest add dialog
pnpx shadcn@latest add table

# Add multiple components
pnpx shadcn@latest add button input dialog table
```

### Available Components

- **Form Components**: Input, Select, Checkbox, Radio, Textarea
- **Navigation**: Button, Link, Breadcrumb, Pagination
- **Layout**: Card, Dialog, Sheet, Tabs, Accordion
- **Data Display**: Table, Badge, Avatar, Progress
- **Feedback**: Alert, Toast, Loading, Skeleton

## 🛣️ Routing

### TanStack Router

This project uses [TanStack Router](https://tanstack.com/router) with file-based routing.

```
src/routes/
├── __root.tsx              # Root layout
├── index.tsx               # Home page (/)
├── login.tsx               # Login page (/login)
├── dashboard/
│   ├── index.tsx           # Dashboard (/dashboard)
│   ├── orders.tsx          # Orders (/dashboard/orders)
│   └── profile.tsx         # Profile (/dashboard/profile)
├── products/
│   ├── index.tsx           # Products (/products)
│   └── $productId.tsx      # Product detail (/products/:productId)
├── vouchers/
│   ├── index.tsx           # Vouchers (/vouchers)
│   └── $voucherId.tsx      # Voucher detail (/vouchers/:voucherId)
└── admin/
    ├── index.tsx           # Admin dashboard (/admin)
    ├── users.tsx           # User management (/admin/users)
    └── vouchers.tsx        # Voucher management (/admin/vouchers)
```

### Adding Routes

1. **Create route file**
   ```bash
   # TanStack Router will auto-generate the route structure
   touch src/routes/new-page.tsx
   ```

2. **Define route component**
   ```tsx
   import { createFileRoute } from '@tanstack/react-router'

   export const Route = createFileRoute('/new-page')({
     component: NewPage,
   })

   function NewPage() {
     return <div>New Page Content</div>
   }
   ```

### Navigation

```tsx
import { Link } from '@tanstack/react-router'

// Basic link
<Link to="/products">Products</Link>

// Link with parameters
<Link to="/products/$productId" params={{ productId: '123' }}>
  Product 123
</Link>

// Link with search params
<Link to="/products" search={{ category: 'electronics' }}>
  Electronics
</Link>
```
## 📊 State Management

### TanStack Query

This project uses [TanStack Query](https://tanstack.com/query) for server state management.

```tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Query example
function UserProfile() {
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['user', 'profile'],
    queryFn: () => apiClient.getCurrentUser(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>
  return <div>Hello, {user.name}!</div>
}

// Mutation example
function UpdateProfileForm() {
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: apiClient.updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', 'profile'] })
    },
  })

  return (
    <form onSubmit={(e) => {
      e.preventDefault()
      mutation.mutate(formData)
    }}>
      {/* form fields */}
    </form>
  )
}
```

## 🔐 Authentication

### Cookie-Based Authentication

The application uses HTTP-only cookies for secure authentication:

```tsx
// src/lib/auth.tsx
import { createContext, useContext, useEffect, useState } from 'react'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const user = await apiClient.getCurrentUser()
      setUser(user)
    } catch (error) {
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    const response = await apiClient.login(email, password)
    setUser(response.user)
  }

  const logout = async () => {
    await apiClient.logout()
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider')
  }
  return context
}
```

### Protected Routes

```tsx
import { createFileRoute, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/dashboard')({
  beforeLoad: ({ context }) => {
    if (!context.auth.user) {
      throw redirect({
        to: '/login',
        search: { redirect: '/dashboard' },
      })
    }
  },
  component: Dashboard,
})
```

## 🌐 API Integration

### API Client

```tsx
// src/lib/api.ts
class ApiClient {
  private baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'

  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      credentials: 'include', // Include HTTP-only cookies
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`)
    }

    return response.json()
  }

  // Authentication
  async login(email: string, password: string) {
    return this.request('/api/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  }

  async logout() {
    return this.request('/api/logout', { method: 'POST' })
  }

  async getCurrentUser() {
    return this.request('/api/users/me')
  }

  // Products
  async getProducts(params?: ProductFilters) {
    const searchParams = new URLSearchParams(params)
    return this.request(`/api/products?${searchParams}`)
  }

  // Vouchers
  async getVouchers(params?: VoucherFilters) {
    const searchParams = new URLSearchParams(params)
    return this.request(`/api/vouchers?${searchParams}`)
  }

  // Orders
  async createOrder(orderData: CreateOrderRequest) {
    return this.request('/api/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    })
  }
}

export const apiClient = new ApiClient()
```

## 🚀 Deployment

### Production Build

```bash
# Build for production
bun run build

# Preview production build
bun run preview

# Build with analysis
bun run build:analyze
```

### Docker Deployment

```dockerfile
# Multi-stage build
FROM oven/bun:1 AS builder
WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile
COPY . .
RUN bun run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 📚 Additional Resources

- [React Documentation](https://react.dev/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Vite Documentation](https://vitejs.dev/)
- [TanStack Router](https://tanstack.com/router)
- [TanStack Query](https://tanstack.com/query)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn/ui](https://ui.shadcn.com/)
- [Vitest](https://vitest.dev/)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

To use SPA (Single Page Application) navigation you will need to import the `Link` component from `@tanstack/react-router`.

```tsx
import { Link } from "@tanstack/react-router";
```

Then anywhere in your JSX you can use it like so:

```tsx
<Link to="/about">About</Link>
```

This will create a link that will navigate to the `/about` route.

More information on the `Link` component can be found in the [Link documentation](https://tanstack.com/router/v1/docs/framework/react/api/router/linkComponent).

### Using A Layout

In the File Based Routing setup the layout is located in `src/routes/__root.tsx`. Anything you add to the root route will appear in all the routes. The route content will appear in the JSX where you use the `<Outlet />` component.

Here is an example layout that includes a header:

```tsx
import { Outlet, createRootRoute } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";

import { Link } from "@tanstack/react-router";

export const Route = createRootRoute({
  component: () => (
    <>
      <header>
        <nav>
          <Link to="/">Home</Link>
          <Link to="/about">About</Link>
        </nav>
      </header>
      <Outlet />
      <TanStackRouterDevtools />
    </>
  ),
});
```

The `<TanStackRouterDevtools />` component is not required so you can remove it if you don't want it in your layout.

More information on layouts can be found in the [Layouts documentation](https://tanstack.com/router/latest/docs/framework/react/guide/routing-concepts#layouts).

## Data Fetching

There are multiple ways to fetch data in your application. You can use TanStack Query to fetch data from a server. But you can also use the `loader` functionality built into TanStack Router to load the data for a route before it's rendered.

For example:

```tsx
const peopleRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/people",
  loader: async () => {
    const response = await fetch("https://swapi.dev/api/people");
    return response.json() as Promise<{
      results: {
        name: string;
      }[];
    }>;
  },
  component: () => {
    const data = peopleRoute.useLoaderData();
    return (
      <ul>
        {data.results.map((person) => (
          <li key={person.name}>{person.name}</li>
        ))}
      </ul>
    );
  },
});
```

Loaders simplify your data fetching logic dramatically. Check out more information in the [Loader documentation](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#loader-parameters).

### React-Query

React-Query is an excellent addition or alternative to route loading and integrating it into you application is a breeze.

First add your dependencies:

```bash
bun install @tanstack/react-query @tanstack/react-query-devtools
```

Next we'll need to create a query client and provider. We recommend putting those in `main.tsx`.

```tsx
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// ...

const queryClient = new QueryClient();

// ...

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);

  root.render(
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>,
  );
}
```

You can also add TanStack Query Devtools to the root route (optional).

```tsx
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

const rootRoute = createRootRoute({
  component: () => (
    <>
      <Outlet />
      <ReactQueryDevtools buttonPosition="top-right" />
      <TanStackRouterDevtools />
    </>
  ),
});
```

Now you can use `useQuery` to fetch your data.

```tsx
import { useQuery } from "@tanstack/react-query";

import "./App.css";

function App() {
  const { data } = useQuery({
    queryKey: ["people"],
    queryFn: () =>
      fetch("https://swapi.dev/api/people")
        .then((res) => res.json())
        .then((data) => data.results as { name: string }[]),
    initialData: [],
  });

  return (
    <div>
      <ul>
        {data.map((person) => (
          <li key={person.name}>{person.name}</li>
        ))}
      </ul>
    </div>
  );
}

export default App;
```

You can find out everything you need to know on how to use React-Query in the [React-Query documentation](https://tanstack.com/query/latest/docs/framework/react/overview).

## State Management

Another common requirement for React applications is state management. There are many options for state management in React. TanStack Store provides a great starting point for your project.

First you need to add TanStack Store as a dependency:

```bash
bun install @tanstack/store
```

Now let's create a simple counter in the `src/App.tsx` file as a demonstration.

```tsx
import { useStore } from "@tanstack/react-store";
import { Store } from "@tanstack/store";
import "./App.css";

const countStore = new Store(0);

function App() {
  const count = useStore(countStore);
  return (
    <div>
      <button onClick={() => countStore.setState((n) => n + 1)}>
        Increment - {count}
      </button>
    </div>
  );
}

export default App;
```

One of the many nice features of TanStack Store is the ability to derive state from other state. That derived state will update when the base state updates.

Let's check this out by doubling the count using derived state.

```tsx
import { useStore } from "@tanstack/react-store";
import { Store, Derived } from "@tanstack/store";
import "./App.css";

const countStore = new Store(0);

const doubledStore = new Derived({
  fn: () => countStore.state * 2,
  deps: [countStore],
});
doubledStore.mount();

function App() {
  const count = useStore(countStore);
  const doubledCount = useStore(doubledStore);

  return (
    <div>
      <button onClick={() => countStore.setState((n) => n + 1)}>
        Increment - {count}
      </button>
      <div>Doubled - {doubledCount}</div>
    </div>
  );
}

export default App;
```

We use the `Derived` class to create a new store that is derived from another store. The `Derived` class has a `mount` method that will start the derived store updating.

Once we've created the derived store we can use it in the `App` component just like we would any other store using the `useStore` hook.

You can find out everything you need to know on how to use TanStack Store in the [TanStack Store documentation](https://tanstack.com/store/latest).

# Demo files

Files prefixed with `demo` can be safely deleted. They are there to provide a starting point for you to play around with the features you've installed.

# Learn More

You can learn more about all of the offerings from TanStack in the [TanStack documentation](https://tanstack.com).
