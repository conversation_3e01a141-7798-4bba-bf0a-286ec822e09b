# Coupon Voucher Service

A comprehensive voucher management service for the coupon microservice system, built with Go and gRPC. This service handles voucher creation, validation, eligibility checks, redemption, and publishes voucher events to Kafka.

## 🎯 Service Overview

The Coupon Voucher Service provides:

- **Voucher Management**: CRUD operations for voucher lifecycle
- **Eligibility Checking**: Complex business logic for voucher eligibility
- **Discount Calculations**: Support for percentage and fixed amount discounts
- **Usage Tracking**: Monitor voucher usage and limits
- **Event Publishing**: Kafka events for voucher lifecycle changes
- **Health Monitoring**: Comprehensive health checks and metrics

### Key Features

- **Multiple Discount Types**: Percentage and fixed amount discounts
- **User Eligibility**: User type-based eligibility (REGULAR, PREMIUM, VIP)
- **Product Restrictions**: Category and product-specific vouchers
- **Usage Limits**: Per-user and total usage limits
- **Expiration Management**: Time-based voucher expiration
- **Event-Driven Architecture**: Kafka integration for voucher events
- **Database Integration**: PostgreSQL with GORM for data persistence and auto-migration
- **Redis Caching**: High-performance caching for voucher data
- **gRPC Authentication Middleware**: Service-to-service authentication
- **Distributed Tracing**: Jaeger integration for request tracing
- **Metrics Collection**: Prometheus metrics for monitoring and alerting

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│  Voucher Service │───▶│   PostgreSQL    │
│                 │    │                  │    │                 │
│ - Voucher APIs  │    │ - Voucher CRUD   │    │ - Vouchers      │
│ - Eligibility   │    │ - Eligibility    │    │ - Usage Logs    │
│ - Validation    │    │ - Validation     │    │ - Restrictions  │
└─────────────────┘    │ - Event Publish  │    └─────────────────┘
                       │ - Health Checks  │
┌─────────────────┐    └──────────────────┘    ┌─────────────────┐
│ Other Services  │                            │      Redis      │
│                 │◀───────────────────────────│                 │
│ - Order Service │    Voucher Data            │ - Voucher Cache │
│ - User Service  │    (voucher details,       │ - Usage Cache   │
│ - Product Svc   │     eligibility)           │ - Eligibility   │
└─────────────────┘                            └─────────────────┘

                       ┌──────────────────┐
                       │      Kafka       │
                       │                  │
                       │ - Voucher Events │
                       │ - Usage Events   │
                       │ - Expiry Events  │
                       └──────────────────┘
```

## 📋 gRPC API Methods

### Voucher Management
- `CreateVoucher(CreateVoucherRequest) → CreateVoucherResponse` - Create new voucher
- `GetVoucher(GetVoucherRequest) → GetVoucherResponse` - Get voucher by ID
- `GetVoucherByCode(GetVoucherByCodeRequest) → GetVoucherByCodeResponse` - Get voucher by code
- `UpdateVoucher(UpdateVoucherRequest) → UpdateVoucherResponse` - Update voucher details
- `DeleteVoucher(DeleteVoucherRequest) → DeleteVoucherResponse` - Soft delete voucher
- `ListVouchers(ListVouchersRequest) → ListVouchersResponse` - List vouchers with pagination

### Voucher Operations
- `ValidateVoucher(ValidateVoucherRequest) → ValidateVoucherResponse` - Validate voucher for use
- `CheckEligibility(CheckEligibilityRequest) → CheckEligibilityResponse` - Check user eligibility
- `UseVoucher(UseVoucherRequest) → UseVoucherResponse` - Redeem voucher
- `GetUserVouchers(GetUserVouchersRequest) → GetUserVouchersResponse` - Get user's available vouchers

### Health Check
- `HealthCheck(HealthCheckRequest) → HealthCheckResponse` - Service health status

## 🔧 Configuration

### Environment Variables

```bash
# Service Configuration
VOUCHER_SERVICE_CLIENT_ID=voucher-service-client-id
VOUCHER_SERVICE_CLIENT_KEY=voucher-service-client-secret

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=coupon_voucher
POSTGRES_SSLMODE=disable

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Auth Service Configuration
AUTH_SERVICE_ADDR=auth-service:50051

# User Service Configuration
USER_SERVICE_ADDR=user-service:50051

# Product Service Configuration
PRODUCT_SERVICE_ADDR=product-service:50051

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_TOPIC_VOUCHER_EVENTS=voucher-events

# Observability
JAEGER_HOST=jaeger
JAEGER_PORT=6831
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Metrics
METRICS_ENABLED=true
METRICS_PUSHGATEWAY_URL=http://pushgateway:9091
METRICS_PUSH_INTERVAL=30s
METRICS_INSTANCE_ID=voucher-service-1
```

## 🗄️ Database Schema

### Vouchers Table
```sql
CREATE TABLE vouchers (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    discount_type VARCHAR(20) NOT NULL, -- 'PERCENTAGE' or 'FIXED_AMOUNT'
    discount_value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2),
    max_discount_amount DECIMAL(10,2),
    usage_limit INTEGER,
    usage_limit_per_user INTEGER,
    current_usage INTEGER DEFAULT 0,
    eligible_user_types TEXT[], -- Array of user types
    eligible_categories INTEGER[], -- Array of category IDs
    eligible_products INTEGER[], -- Array of product IDs
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Voucher Usage Table
```sql
CREATE TABLE voucher_usage (
    id SERIAL PRIMARY KEY,
    voucher_id INTEGER REFERENCES vouchers(id),
    user_id INTEGER NOT NULL,
    order_id INTEGER,
    discount_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Discount Types
- `PERCENTAGE` - Percentage-based discount (e.g., 10% off)
- `FIXED_AMOUNT` - Fixed amount discount (e.g., $5 off)

### User Types Eligibility
- `REGULAR` - Standard users
- `PREMIUM` - Premium users
- `VIP` - VIP users
- `ALL` - All user types
## 🚀 Getting Started

### Prerequisites
- Go >= 1.24
- PostgreSQL 13+
- Redis 6+
- Kafka 2.8+
- Docker and Docker Compose

### Local Development
1. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Build and run**
   ```bash
   make build
   make run
   ```

3. **Access the service**
   - gRPC Server: `localhost:50054`
   - HTTP Health Check: `http://localhost:8083/health`
   - Metrics: `http://localhost:8083/metrics`

### Docker Deployment
```bash
# Start with all dependencies
make compose-up

# Stop services
make compose-down
```

## 📨 Kafka Events

### Published Events

#### Voucher Created Event
```json
{
  "event_type": "voucher.created",
  "voucher_id": 123,
  "code": "SAVE10",
  "discount_type": "PERCENTAGE",
  "discount_value": 10.00,
  "timestamp": "2023-12-01T10:00:00Z"
}
```

#### Voucher Used Event
```json
{
  "event_type": "voucher.used",
  "voucher_id": 123,
  "user_id": 456,
  "order_id": 789,
  "discount_amount": 5.00,
  "timestamp": "2023-12-01T10:00:00Z"
}
```

#### Voucher Expired Event
```json
{
  "event_type": "voucher.expired",
  "voucher_id": 123,
  "code": "SAVE10",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 🧪 Testing

### Unit Tests
```bash
make test
```

### gRPC Testing
```bash
# Create voucher
grpcurl -plaintext -d '{"code":"SAVE10","name":"10% Off","discount_type":"PERCENTAGE","discount_value":10.0,"start_date":"2023-12-01T00:00:00Z","end_date":"2023-12-31T23:59:59Z"}' \
  localhost:50054 voucher.VoucherService/CreateVoucher

# Get voucher by code
grpcurl -plaintext -d '{"code":"SAVE10"}' \
  localhost:50054 voucher.VoucherService/GetVoucherByCode

# Check eligibility
grpcurl -plaintext -d '{"voucher_id":1,"user_id":123,"order_amount":50.0}' \
  localhost:50054 voucher.VoucherService/CheckEligibility

# Use voucher
grpcurl -plaintext -d '{"voucher_id":1,"user_id":123,"order_id":456,"order_amount":50.0}' \
  localhost:50054 voucher.VoucherService/UseVoucher
```

## 📊 Monitoring

### Health Checks
```bash
curl http://localhost:8083/health
```

### Metrics
- Voucher creation and usage rates
- Eligibility check success/failure rates
- Discount amount distributions
- Cache hit/miss ratios
- Database connection pool stats
- Kafka producer metrics
- gRPC request metrics

## 🔧 Development

### Project Structure
```
coupon-voucher-service/
├── cmd/server/main.go              # Application entry point
├── internal/
│   ├── service/voucher_service.go  # Core business logic
│   ├── repository/voucher_repo.go  # Data access layer
│   ├── model/                      # Data models
│   │   ├── voucher.go
│   │   └── voucher_usage.go
│   ├── cache/redis_cache.go        # Redis caching layer
│   ├── kafka/producer.go           # Kafka event publisher
│   ├── clients/                    # gRPC clients
│   │   ├── user_client.go
│   │   └── product_client.go
│   └── middleware/grpc_auth.go     # Authentication middleware
├── config/config.yaml              # Service configuration
├── Dockerfile                      # Container image
├── docker-compose.yml              # Local development
└── Makefile                        # Build commands
```

## 🎯 Business Logic

### Eligibility Rules
1. **User Type Check**: Voucher must be eligible for user's type
2. **Product/Category Check**: Order items must match voucher restrictions
3. **Minimum Order Amount**: Order total must meet minimum requirement
4. **Usage Limits**: Check per-user and total usage limits
5. **Date Validity**: Voucher must be within valid date range
6. **Active Status**: Voucher must be active

### Discount Calculation
- **Percentage Discount**: `discount = (order_amount * discount_value) / 100`
- **Fixed Amount Discount**: `discount = discount_value`
- **Maximum Discount**: Applied if `max_discount_amount` is set
- **Final Discount**: `min(calculated_discount, max_discount_amount)`

## 🚨 Error Handling

### gRPC Error Codes
- `OK` - Successful operation
- `INVALID_ARGUMENT` - Invalid request parameters
- `NOT_FOUND` - Voucher not found
- `ALREADY_EXISTS` - Duplicate voucher code
- `FAILED_PRECONDITION` - Voucher not eligible or expired
- `RESOURCE_EXHAUSTED` - Usage limit exceeded
- `INTERNAL` - Internal server error

## 🔒 Security Features

- **Service Authentication**: gRPC middleware with client credentials
- **Input Validation**: Comprehensive request validation
- **Usage Tracking**: Prevent voucher abuse
- **Audit Logging**: Voucher usage logging for security monitoring
- **Rate Limiting**: Prevent excessive voucher validation requests

## 📚 Additional Resources

- [gRPC Go Documentation](https://grpc.io/docs/languages/go/)
- [GORM Documentation](https://gorm.io/docs/)
- [Redis Go Client](https://github.com/go-redis/redis)
- [Kafka Go Client](https://github.com/segmentio/kafka-go)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
