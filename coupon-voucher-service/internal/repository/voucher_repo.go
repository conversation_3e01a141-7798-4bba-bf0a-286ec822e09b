package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
	"gorm.io/gorm"
)

const (
	voucherCachePrefixByID   = "voucher:id:"
	voucherCachePrefixByCode = "voucher:code:"
	voucherCacheTTL          = 15 * time.Minute
	discountTypesCacheKey    = "discount_types:all"
	discountTypesCacheTTL    = 30 * time.Minute
)

type VoucherRepository interface {
	Create(ctx context.Context, req *model.CreateVoucherRequest, createdBy uint64) (*model.Voucher, error)
	GetByID(ctx context.Context, id uint64) (*model.Voucher, error)
	GetByCode(ctx context.Context, code string) (*model.Voucher, error)
	Update(ctx context.Context, id uint64, req *model.UpdateVoucherRequest) error
	List(ctx context.Context, req *model.ListVouchersRequest) ([]*model.VoucherListItem, int, error)

	GetDiscountTypes(ctx context.Context) ([]*model.DiscountType, error)

	GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error)
	CheckVoucherEligibility(ctx context.Context, req *model.VoucherEligibilityRequest) (*model.VoucherEligibilityResponse, error)
	GetEligibleAutoVouchers(ctx context.Context, req *model.AutoVoucherEligibilityRequest) ([]*model.VoucherEligibilityResponse, error)

	IncrementUsageCount(ctx context.Context, id uint64) (int, error)

	GetUserEligibilityRules(ctx context.Context, voucherID uint64) ([]*model.VoucherUserEligibility, error)
	ReplaceProductRestrictions(ctx context.Context, voucherID uint64, restrictions []*model.VoucherProductRestriction) error
	ReplaceTimeRestrictions(ctx context.Context, voucherID uint64, restrictions []*model.VoucherTimeRestriction) error
	ReplaceUserEligibility(ctx context.Context, voucherID uint64, rules []*model.VoucherUserEligibility) error
}

type voucherRepository struct {
	db          *database.DB
	redis       *redis.Client
	logger      *logging.Logger
	orderClient *clients.OrderClient
	userClient  *clients.UserClient
}

func NewVoucherRepository(db *database.DB, redis *redis.Client, logger *logging.Logger, orderClient *clients.OrderClient, userClient *clients.UserClient) VoucherRepository {
	return &voucherRepository{db: db, redis: redis, logger: logger, orderClient: orderClient, userClient: userClient}
}

func (r *voucherRepository) getVoucherFromCache(ctx context.Context, key string) (*model.Voucher, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, key)
	if err != nil || val == "" {
		return nil, err
	}

	log.Debugf("Cache hit for key: %s", key)
	var voucher model.Voucher
	if err := json.Unmarshal([]byte(val), &voucher); err != nil {
		log.Errorf("Failed to unmarshal voucher from cache for key %s: %v", key, err)
		return nil, err
	}
	return &voucher, nil
}

func (r *voucherRepository) setVoucherInCache(ctx context.Context, voucher *model.Voucher) {
	log := r.logger.WithContext(ctx)

	voucherBytes, err := json.Marshal(voucher)
	if err != nil {
		log.Errorf("Failed to marshal voucher for caching (ID: %d): %v", voucher.ID, err)
		return
	}

	keyByID := fmt.Sprintf("%s%d", voucherCachePrefixByID, voucher.ID)
	if err := r.redis.Set(ctx, keyByID, voucherBytes, voucherCacheTTL); err != nil {
		log.Errorf("Failed to set voucher cache for key %s: %v", keyByID, err)
	}

	keyByCode := fmt.Sprintf("%s%s", voucherCachePrefixByCode, voucher.VoucherCode)
	if err := r.redis.Set(ctx, keyByCode, voucherBytes, voucherCacheTTL); err != nil {
		log.Errorf("Failed to set voucher cache for key %s: %v", keyByCode, err)
	}
}

func (r *voucherRepository) invalidateVoucherCache(ctx context.Context, voucher *model.Voucher) {
	keyByID := fmt.Sprintf("%s%d", voucherCachePrefixByID, voucher.ID)
	keyByCode := fmt.Sprintf("%s%s", voucherCachePrefixByCode, voucher.VoucherCode)

	if err := r.redis.Del(ctx, keyByID, keyByCode); err != nil {
		r.logger.WithContext(ctx).Errorf("Failed to invalidate voucher cache for voucher %d: %v", voucher.ID, err)
	}
}

func (r *voucherRepository) Create(ctx context.Context, req *model.CreateVoucherRequest, createdBy uint64) (*model.Voucher, error) {
	voucher := &model.Voucher{
		VoucherCode:       req.VoucherCode,
		Title:             req.Title,
		Description:       req.Description,
		DiscountTypeID:    req.DiscountTypeID,
		DiscountValue:     req.DiscountValue,
		UsageMethod:       req.UsageMethod,
		ValidFrom:         req.ValidFrom,
		ValidUntil:        req.ValidUntil,
		MaxUsageCount:     req.MaxUsageCount,
		MaxUsagePerUser:   req.MaxUsagePerUser,
		MinOrderAmount:    req.MinOrderAmount,
		MaxDiscountAmount: req.MaxDiscountAmount,
		CreatedBy:         createdBy,
		Status:            model.VoucherStatusActive,
	}

	if err := r.db.WithContext(ctx).Create(voucher).Error; err != nil {
		return nil, err
	}

	return r.GetByID(ctx, voucher.ID)
}

func (r *voucherRepository) GetByID(ctx context.Context, id uint64) (*model.Voucher, error) {
	cacheKey := fmt.Sprintf("%s%d", voucherCachePrefixByID, id)

	cachedVoucher, _ := r.getVoucherFromCache(ctx, cacheKey)
	if cachedVoucher != nil {
		return cachedVoucher, nil
	}

	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&voucher).Error; err != nil {
		return nil, err
	}

	if err := r.loadRelatedData(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %d: %v", id, err)
	}

	if err := r.calculateVoucherStatistics(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to calculate voucher statistics for voucher %d: %v", id, err)
	}

	if err := r.loadUserUsageDetails(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load user usage details for voucher %d: %v", id, err)
	}

	r.setVoucherInCache(ctx, &voucher)

	return &voucher, nil
}

func (r *voucherRepository) GetByCode(ctx context.Context, code string) (*model.Voucher, error) {
	cacheKey := fmt.Sprintf("%s%s", voucherCachePrefixByCode, code)

	cachedVoucher, _ := r.getVoucherFromCache(ctx, cacheKey)
	if cachedVoucher != nil {
		return cachedVoucher, nil
	}

	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Where("voucher_code = ?", code).First(&voucher).Error; err != nil {
		return nil, err
	}

	if err := r.loadRelatedData(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", code, err)
	}

	if err := r.calculateVoucherStatistics(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to calculate voucher statistics for voucher %s: %v", code, err)
	}

	if err := r.loadUserUsageDetails(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load user usage details for voucher %s: %v", code, err)
	}

	r.setVoucherInCache(ctx, &voucher)

	return &voucher, nil
}

func (r *voucherRepository) GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error) {
	var vouchers []*model.Voucher
	err := r.db.WithContext(ctx).
		Where("usage_method = ?", model.UsageMethodAutomatic).
		Where("status = ?", model.VoucherStatusActive).
		Where("valid_from <= ?", time.Now()).
		Where("valid_until >= ?", time.Now()).
		Where("min_order_amount <= ?", orderAmount).
		Where("max_usage_count IS NULL OR current_usage_count < max_usage_count").
		Find(&vouchers).Error
	return vouchers, err
}

func (r *voucherRepository) loadRelatedData(ctx context.Context, voucher *model.Voucher) error {
	var discountType model.DiscountType
	if err := r.db.WithContext(ctx).Where("id = ?", voucher.DiscountTypeID).First(&discountType).Error; err == nil {
		voucher.DiscountType = &discountType
	}

	return r.loadOtherRelatedData(ctx, voucher)
}

func (r *voucherRepository) loadOtherRelatedData(ctx context.Context, voucher *model.Voucher) error {
	var productRestrictions []*model.VoucherProductRestriction
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&productRestrictions).Error; err == nil {
		voucher.ProductRestrictions = productRestrictions
	}

	var timeRestrictions []*model.VoucherTimeRestriction
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&timeRestrictions).Error; err == nil {
		voucher.TimeRestrictions = timeRestrictions
	}

	var userEligibility []*model.VoucherUserEligibility
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&userEligibility).Error; err == nil {
		voucher.UserEligibilityRules = userEligibility
		voucher.UserEligibilityType = r.determineUserEligibilityType(userEligibility)
	}

	return nil
}

func (r *voucherRepository) determineUserEligibilityType(rules []*model.VoucherUserEligibility) string {
	if len(rules) == 0 {
		return "ALL_USERS"
	}

	hasSpecificUsers := false
	hasUserTypes := false
	hasAccountAgeRules := false
	hasOrderCountRules := false

	for _, rule := range rules {
		if rule.UserID != nil {
			hasSpecificUsers = true
		}
		if rule.UserType != nil {
			hasUserTypes = true
		}
		if rule.MinAccountAgeDays != nil || rule.MaxAccountAgeDays != nil {
			hasAccountAgeRules = true
		}
		if rule.MinPreviousOrders != nil || rule.MaxPreviousOrders != nil {
			hasOrderCountRules = true
		}
	}

	if hasSpecificUsers {
		return "SPECIFIC_USERS"
	} else if hasUserTypes {
		return "USER_TYPE_BASED"
	} else if hasAccountAgeRules && hasOrderCountRules {
		return "ACCOUNT_AND_ORDER_BASED"
	} else if hasAccountAgeRules {
		return "ACCOUNT_AGE_BASED"
	} else if hasOrderCountRules {
		return "ORDER_COUNT_BASED"
	}

	return "RULE_BASED"
}

func (r *voucherRepository) Update(ctx context.Context, id uint64, req *model.UpdateVoucherRequest) error {
	voucher, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	err = r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		updates := map[string]any{
			"title":               req.Title,
			"description":         req.Description,
			"discount_type_id":    req.DiscountTypeID,
			"discount_value":      req.DiscountValue,
			"usage_method":        req.UsageMethod,
			"status":              req.Status,
			"min_order_amount":    req.MinOrderAmount,
			"max_discount_amount": req.MaxDiscountAmount,
			"max_usage_count":     req.MaxUsageCount,
			"max_usage_per_user":  req.MaxUsagePerUser,
			"valid_from":          req.ValidFrom,
			"valid_until":         req.ValidUntil,
		}

		if err := tx.Model(&model.Voucher{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			return err
		}

		if err := r.replaceProductRestrictionsInTx(ctx, tx, id, req.ProductRestrictions); err != nil {
			return err
		}

		if err := r.replaceTimeRestrictionsInTx(ctx, tx, id, req.TimeRestrictions); err != nil {
			return err
		}

		if err := r.replaceUserEligibilityInTx(ctx, tx, id, req.UserEligibility); err != nil {
			return err
		}

		return nil
	})

	if err == nil {
		r.invalidateVoucherCache(ctx, voucher)
	}

	return err
}

func (r *voucherRepository) List(ctx context.Context, req *model.ListVouchersRequest) ([]*model.VoucherListItem, int, error) {
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Voucher{}).
		Select(`vouchers.id,
				vouchers.voucher_code,
				vouchers.title,
				discount_types.type_name as discount_type_name,
				discount_types.type_code as discount_type_code,
				vouchers.discount_value,
				vouchers.usage_method,
				vouchers.valid_from,
				vouchers.valid_until,
				vouchers.created_by,
				vouchers.status,
				vouchers.created_at,
				vouchers.updated_at`).
		Joins("LEFT JOIN discount_types ON vouchers.discount_type_id = discount_types.id")

	if req.Search != "" {
		query = query.Where("vouchers.title ILIKE ? OR vouchers.voucher_code ILIKE ? OR vouchers.description ILIKE ?",
			"%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	if req.DiscountTypeID != nil {
		query = query.Where("vouchers.discount_type_id = ?", *req.DiscountTypeID)
	}

	if req.UsageMethod != nil {
		query = query.Where("vouchers.usage_method = ?", *req.UsageMethod)
	}

	if req.Status != "" {
		query = query.Where("vouchers.status = ?", req.Status)
	}

	countQuery := r.db.WithContext(ctx).Model(&model.Voucher{})
	if req.Search != "" {
		countQuery = countQuery.Where("title ILIKE ? OR voucher_code ILIKE ? OR description ILIKE ?",
			"%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}
	if req.DiscountTypeID != nil {
		countQuery = countQuery.Where("discount_type_id = ?", *req.DiscountTypeID)
	}
	if req.UsageMethod != nil {
		countQuery = countQuery.Where("usage_method = ?", *req.UsageMethod)
	}
	if req.Status != "" {
		countQuery = countQuery.Where("status = ?", req.Status)
	}
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "vouchers.created_at"
	} else {
		sortBy = "vouchers." + sortBy
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	var voucherListData []model.VoucherListItem
	if err := query.Find(&voucherListData).Error; err != nil {
		return nil, 0, err
	}

	var vouchers []*model.VoucherListItem
	for _, data := range voucherListData {
		voucher := &model.VoucherListItem{
			ID:               data.ID,
			VoucherCode:      data.VoucherCode,
			Title:            data.Title,
			DiscountTypeName: data.DiscountTypeName,
			DiscountTypeCode: data.DiscountTypeCode,
			DiscountValue:    data.DiscountValue,
			UsageMethod:      data.UsageMethod,
			ValidFrom:        data.ValidFrom,
			ValidUntil:       data.ValidUntil,
			CreatedBy:        data.CreatedBy,
			Status:           data.Status,
			CreatedAt:        data.CreatedAt,
			UpdatedAt:        data.UpdatedAt,
		}
		vouchers = append(vouchers, voucher)
	}

	return vouchers, int(total), nil
}

func (r *voucherRepository) GetDiscountTypes(ctx context.Context) ([]*model.DiscountType, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, discountTypesCacheKey)
	if err == nil && val != "" {
		log.Debugf("Cache hit for discount types")
		var discountTypes []*model.DiscountType
		if err := json.Unmarshal([]byte(val), &discountTypes); err == nil {
			return discountTypes, nil
		}
		log.Errorf("Failed to unmarshal discount types from cache: %v", err)
	}

	var discountTypes []*model.DiscountType
	err = r.db.WithContext(ctx).Where("is_active = ?", true).Find(&discountTypes).Error
	if err != nil {
		return nil, err
	}

	if discountTypesBytes, err := json.Marshal(discountTypes); err == nil {
		if err := r.redis.Set(ctx, discountTypesCacheKey, discountTypesBytes, discountTypesCacheTTL); err != nil {
			log.Errorf("Failed to cache discount types: %v", err)
		}
	}

	return discountTypes, nil
}

func (r *voucherRepository) CheckVoucherEligibility(ctx context.Context, req *model.VoucherEligibilityRequest) (*model.VoucherEligibilityResponse, error) {
	voucher, err := r.GetByCode(ctx, req.VoucherCode)
	if err != nil {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher not found or inactive",
		}, nil
	}

	if voucher.Status != model.VoucherStatusActive {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher not found or inactive",
		}, nil
	}

	orderTimestamp := req.OrderTimestamp
	if orderTimestamp.IsZero() {
		orderTimestamp = time.Now()
	}

	if orderTimestamp.Before(voucher.ValidFrom) || orderTimestamp.After(voucher.ValidUntil) {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher is expired or not yet valid",
		}, nil
	}

	if voucher.MaxUsageCount != nil && voucher.CurrentUsageCount >= *voucher.MaxUsageCount {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher usage limit exceeded globally",
		}, nil
	}

	userUsageCount, err := r.getUserVoucherUsageCount(ctx, req.UserID, voucher.ID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user voucher usage count: %v", err)
		userUsageCount = 0
	}

	if voucher.MaxUsagePerUser != nil && userUsageCount >= *voucher.MaxUsagePerUser {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "User has exceeded usage limit for this voucher",
		}, nil
	}

	eligible, message, err := r.checkUserEligibility(ctx, voucher, req.UserID, orderTimestamp)
	if err != nil {
		return nil, err
	}
	if !eligible {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  message,
		}, nil
	}

	eligible, message, err = r.checkTimeRestrictions(ctx, voucher, orderTimestamp)
	if err != nil {
		return nil, err
	}
	if !eligible {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  message,
		}, nil
	}

	eligibleOrderAmount, err := r.checkProductRestrictions(ctx, voucher, req.CartItems, req.OrderAmount)
	if err != nil {
		return nil, err
	}
	if eligibleOrderAmount == 0 {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "No eligible products in cart for this voucher",
		}, nil
	}

	orderAmountForCalculation := eligibleOrderAmount
	if len(req.CartItems) == 0 {
		orderAmountForCalculation = req.OrderAmount
	}

	if orderAmountForCalculation < voucher.MinOrderAmount {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Minimum order amount not met",
		}, nil
	}

	discountAmount := r.calculateDiscount(voucher, orderAmountForCalculation)
	voucherStatus := string(voucher.Status)

	return &model.VoucherEligibilityResponse{
		Eligible:         true,
		Message:          "Voucher is eligible",
		VoucherID:        voucher.ID,
		DiscountAmount:   discountAmount,
		DiscountTypeName: voucher.DiscountType.TypeName,
		DiscountTypeCode: voucher.DiscountType.TypeCode,
		VoucherCode:      voucher.VoucherCode,
		Title:            voucher.Title,
		Description:      voucher.Description,
		Status:           &voucherStatus,
	}, nil
}

func (r *voucherRepository) GetEligibleAutoVouchers(ctx context.Context, req *model.AutoVoucherEligibilityRequest) ([]*model.VoucherEligibilityResponse, error) {
	vouchers, err := r.GetActiveAutoVouchers(ctx, req.OrderAmount)
	if err != nil {
		return nil, err
	}

	var eligibleVouchers []*model.VoucherEligibilityResponse

	orderTimestamp := req.OrderTimestamp
	if orderTimestamp.IsZero() {
		orderTimestamp = time.Now()
	}

	for _, voucher := range vouchers {
		if err := r.loadRelatedData(ctx, voucher); err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %d: %v", voucher.ID, err)
			continue
		}

		if voucher.MaxUsagePerUser != nil {
			userUsageCount, err := r.getUserVoucherUsageCount(ctx, req.UserID, voucher.ID)
			if err != nil {
				r.logger.WithContext(ctx).Warnf("Failed to get user voucher usage count: %v", err)
				userUsageCount = 0
			}

			if userUsageCount >= *voucher.MaxUsagePerUser {
				continue
			}
		}

		eligible, _, err := r.checkUserEligibility(ctx, voucher, req.UserID, orderTimestamp)
		if err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to check user eligibility for voucher %d: %v", voucher.ID, err)
			continue
		}
		if !eligible {
			continue
		}

		eligible, _, err = r.checkTimeRestrictions(ctx, voucher, orderTimestamp)
		if err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to check time restrictions for voucher %d: %v", voucher.ID, err)
			continue
		}
		if !eligible {
			continue
		}

		eligibleOrderAmount, err := r.checkProductRestrictions(ctx, voucher, req.CartItems, req.OrderAmount)
		if err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to check product restrictions for voucher %d: %v", voucher.ID, err)
			continue
		}
		if eligibleOrderAmount == 0 {
			continue
		}

		orderAmountForCalculation := eligibleOrderAmount
		if len(req.CartItems) == 0 {
			orderAmountForCalculation = req.OrderAmount
		}

		if orderAmountForCalculation < voucher.MinOrderAmount {
			continue
		}

		discountAmount := r.calculateDiscount(voucher, orderAmountForCalculation)
		voucherStatus := string(voucher.Status)

		discountTypeName := ""
		discountTypeCode := ""
		if voucher.DiscountType != nil {
			discountTypeName = voucher.DiscountType.TypeName
			discountTypeCode = voucher.DiscountType.TypeCode
		}

		eligibleVouchers = append(eligibleVouchers, &model.VoucherEligibilityResponse{
			Eligible:         true,
			Message:          "Voucher is eligible",
			VoucherID:        voucher.ID,
			DiscountAmount:   discountAmount,
			DiscountTypeName: discountTypeName,
			DiscountTypeCode: discountTypeCode,
			VoucherCode:      voucher.VoucherCode,
			Title:            voucher.Title,
			Description:      voucher.Description,
			Status:           &voucherStatus,
		})
	}

	return eligibleVouchers, nil
}

func (r *voucherRepository) getUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int, error) {
	if r.orderClient == nil {
		r.logger.WithContext(ctx).Warn("Order client not available, returning 0 for voucher usage count")
		return 0, nil
	}

	count, err := r.orderClient.GetUserVoucherUsageCount(ctx, userID, voucherID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user voucher usage count from order service: %v", err)
		return 0, err
	}

	return int(count), nil
}

func (r *voucherRepository) getUserOrderCount(ctx context.Context, userID uint64) (uint64, error) {
	if r.orderClient == nil {
		r.logger.WithContext(ctx).Warn("Order client not available, returning 0 for order count")
		return 0, nil
	}

	count, err := r.orderClient.GetUserOrderCount(ctx, userID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user order count from order service: %v", err)
		return 0, err
	}

	return count, nil
}

func (r *voucherRepository) checkUserEligibility(ctx context.Context, voucher *model.Voucher, userID uint64, orderTimestamp time.Time) (bool, string, error) {
	rules, err := r.GetUserEligibilityRules(ctx, voucher.ID)
	if err != nil {
		return false, "", err
	}

	if len(rules) == 0 {
		return true, "", nil
	}

	var userAccountAgeDays int32 = 30
	var userOrderCount uint64 = 0
	userType := "NEW"

	if r.userClient != nil {
		user, err := r.userClient.GetUser(ctx, userID)
		if err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to get user info from user service: %v", err)
		} else if user != nil {
			userType = r.convertUserTypeToString(int32(user.Type))
			accountAge := orderTimestamp.Sub(user.CreatedAt.AsTime())
			userAccountAgeDays = int32(accountAge.Hours() / 24)
		}
	}

	actualOrderCount, err := r.getUserOrderCount(ctx, userID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user order count: %v", err)
	} else {
		userOrderCount = actualOrderCount
	}

	for _, rule := range rules {
		if rule.UserID != nil && *rule.UserID == userID {
			return true, "", nil
		}
	}

	hasSpecificUserRules := false
	for _, rule := range rules {
		if rule.UserID != nil {
			hasSpecificUserRules = true
			break
		}
	}

	if hasSpecificUserRules {
		userAllowedByType := false
		for _, rule := range rules {
			if rule.UserType != nil && *rule.UserType == userType {
				userAllowedByType = true
				break
			}
		}
		if !userAllowedByType {
			return false, "Voucher is not available for this user", nil
		}
	}

	for _, rule := range rules {
		if rule.UserType != nil && *rule.UserType == "VIP" && userType != "VIP" {
			return false, "Voucher is only for VIP users", nil
		}
	}

	for _, rule := range rules {
		if rule.UserID == nil && ((rule.MinPreviousOrders != nil && *rule.MinPreviousOrders == 0) || (rule.MaxPreviousOrders != nil && *rule.MaxPreviousOrders == 0)) {
			if userOrderCount > 0 {
				return false, "Voucher is only for new users", nil
			}
			return true, "", nil
		}
	}

	for _, rule := range rules {
		if rule.UserID == nil && rule.MinPreviousOrders != nil && *rule.MinPreviousOrders > 0 {
			if userOrderCount == 0 {
				return false, "Voucher is only for existing users", nil
			}
			return true, "", nil
		}
	}

	for _, rule := range rules {
		if rule.UserID == nil && (rule.MinAccountAgeDays != nil || rule.MaxAccountAgeDays != nil) && rule.MinPreviousOrders == nil && rule.MaxPreviousOrders == nil {
			if rule.MaxAccountAgeDays != nil && *rule.MaxAccountAgeDays <= 30 {
				if userAccountAgeDays > 30 {
					return false, "Voucher is only for new users", nil
				}
			} else {
				if userAccountAgeDays <= 30 {
					return false, "Voucher is only for existing users", nil
				}
			}
			return true, "", nil
		}
	}

	for _, rule := range rules {
		if rule.UserType != nil && *rule.UserType == userType {
			if rule.MinAccountAgeDays != nil && userAccountAgeDays < *rule.MinAccountAgeDays {
				return false, "User account age requirement not met", nil
			}
			if rule.MaxAccountAgeDays != nil && userAccountAgeDays > *rule.MaxAccountAgeDays {
				return false, "User account age exceeds maximum allowed", nil
			}
			if rule.MinPreviousOrders != nil && userOrderCount < *rule.MinPreviousOrders {
				return false, "User does not meet minimum order requirement", nil
			}
			if rule.MaxPreviousOrders != nil && userOrderCount > *rule.MaxPreviousOrders {
				return false, "User exceeds maximum order limit", nil
			}
			return true, "", nil
		}
	}

	return false, "Voucher is not available for this user", nil
}

func (r *voucherRepository) IncrementUsageCount(ctx context.Context, id uint64) (int, error) {
	var newUsageCount int
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		result := tx.Model(&model.Voucher{}).
			Where("id = ?", id).
			UpdateColumn("current_usage_count", gorm.Expr("current_usage_count + 1"))

		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("voucher with id %d not found", id)
		}

		var voucher model.Voucher
		if err := tx.Select("current_usage_count").Where("id = ?", id).First(&voucher).Error; err != nil {
			return err
		}

		newUsageCount = voucher.CurrentUsageCount
		return nil
	})
	if err != nil {
		return 0, err
	}

	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Select("id", "voucher_code").Where("id = ?", id).First(&voucher).Error; err == nil {
		r.invalidateVoucherCache(ctx, &voucher)
	}

	return newUsageCount, nil
}

func (r *voucherRepository) checkTimeRestrictions(ctx context.Context, voucher *model.Voucher, orderTimestamp time.Time) (bool, string, error) {
	var timeRestrictions []*model.VoucherTimeRestriction
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&timeRestrictions).Error
	if err != nil {
		return false, "", err
	}

	if len(timeRestrictions) == 0 {
		return true, "", nil
	}

	localTimestamp := orderTimestamp
	currentHour := int32(localTimestamp.Hour())
	currentDayOfWeek := int32(localTimestamp.Weekday())
	currentDate := localTimestamp.Truncate(24 * time.Hour)

	r.logger.WithContext(ctx).Debugf("Time restriction check - Timestamp: %v, Hour: %d, DayOfWeek: %d, Date: %v",
		localTimestamp, currentHour, currentDayOfWeek, currentDate)

	for _, restriction := range timeRestrictions {
		valid := false

		switch restriction.RestrictionType {
		case model.TimeRestrictionTypeDaysOfWeek:
			if len(restriction.AllowedDaysOfWeek) > 0 {
				valid = slices.Contains(restriction.AllowedDaysOfWeek, currentDayOfWeek)
			}

		case model.TimeRestrictionTypeHoursOfDay:
			if restriction.AllowedHoursStart != nil && restriction.AllowedHoursEnd != nil {
				start := *restriction.AllowedHoursStart
				end := *restriction.AllowedHoursEnd

				if start <= end {
					valid = currentHour >= start && currentHour <= end
				} else {
					valid = currentHour >= start || currentHour <= end
				}
			}

		case model.TimeRestrictionTypeSpecificDates:
			if len(restriction.SpecificDates) > 0 {
				for _, specificDate := range restriction.SpecificDates {
					if currentDate.Equal(specificDate.Truncate(24 * time.Hour)) {
						valid = true
						break
					}
				}
			}

		case model.TimeRestrictionTypeRecurringDates:
			if restriction.RecurrencePattern != nil {
				r.logger.WithContext(ctx).Debugf("Checking recurring pattern: %v, RecurrenceDayOfMonth: %v",
					*restriction.RecurrencePattern, restriction.RecurrenceDayOfMonth)
				valid = r.checkRecurrencePattern(localTimestamp, *restriction.RecurrencePattern, restriction)
				r.logger.WithContext(ctx).Debugf("Recurring pattern result: %t", valid)
			}

		default:
			valid = true
		}

		if !valid {
			return false, "Voucher is not valid at this time", nil
		}
	}

	return true, "", nil
}

func (r *voucherRepository) checkRecurrencePattern(timestamp time.Time, pattern model.RecurrencePattern, restriction *model.VoucherTimeRestriction) bool {
	switch pattern {
	case model.RecurrencePatternDaily:
		return true

	case model.RecurrencePatternWeekly:
		if restriction.RecurrenceDayOfWeek != nil {
			return int32(timestamp.Weekday()) == *restriction.RecurrenceDayOfWeek
		}

	case model.RecurrencePatternMonthly:
		if restriction.RecurrenceDayOfMonth != nil {
			return int32(timestamp.Day()) == *restriction.RecurrenceDayOfMonth
		}

	case model.RecurrencePatternYearly:
		if restriction.RecurrenceMonth != nil && restriction.RecurrenceDayOfMonth != nil {
			return int32(timestamp.Month()) == *restriction.RecurrenceMonth &&
				int32(timestamp.Day()) == *restriction.RecurrenceDayOfMonth
		}

	case model.RecurrencePatternQuarterly:
		if restriction.RecurrenceDayOfMonth != nil {
			return int32(timestamp.Day()) == *restriction.RecurrenceDayOfMonth &&
				(int32(timestamp.Month())%3) == (*restriction.RecurrenceMonth%3)
		}
	}

	return false
}

func (r *voucherRepository) checkProductRestrictions(ctx context.Context, voucher *model.Voucher, cartItems []model.CartItem, orderAmount float64) (float64, error) {
	var productRestrictions []*model.VoucherProductRestriction
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&productRestrictions).Error
	if err != nil {
		return 0, err
	}

	if len(productRestrictions) == 0 {
		return orderAmount, nil
	}

	if len(cartItems) == 0 {
		return orderAmount, nil
	}

	eligibleOrderAmount := 0.0

	for _, cartItem := range cartItems {
		productAllowed := false

		r.logger.WithContext(ctx).Debugf("Checking cart item: ProductID=%v, CategoryID=%v, Price=%f",
			cartItem.ProductID, cartItem.CategoryID, cartItem.Price)

		for _, restriction := range productRestrictions {
			r.logger.WithContext(ctx).Debugf("Checking restriction: ProductID=%v, CategoryID=%v, IsIncluded=%t",
				restriction.ProductID, restriction.CategoryID, restriction.IsIncluded)

			if restriction.IsIncluded {
				if (restriction.ProductID != nil && cartItem.ProductID != nil && *restriction.ProductID == *cartItem.ProductID) ||
					(restriction.CategoryID != nil && cartItem.CategoryID != nil && *restriction.CategoryID == *cartItem.CategoryID) {
					productAllowed = true
					r.logger.WithContext(ctx).Debugf("Product allowed by inclusion rule")
					break
				}
			}
		}

		if !productAllowed {
			hasInclusionRules := false
			for _, restriction := range productRestrictions {
				if restriction.IsIncluded {
					hasInclusionRules = true
					break
				}
			}

			if hasInclusionRules {
				productAllowed = false
				r.logger.WithContext(ctx).Debugf("Product rejected: has inclusion rules but product doesn't match")
			} else {
				productAllowed = true
				for _, restriction := range productRestrictions {
					if !restriction.IsIncluded {
						if (restriction.ProductID != nil && cartItem.ProductID != nil && *restriction.ProductID == *cartItem.ProductID) ||
							(restriction.CategoryID != nil && cartItem.CategoryID != nil && *restriction.CategoryID == *cartItem.CategoryID) {
							productAllowed = false
							r.logger.WithContext(ctx).Debugf("Product rejected: matches exclusion rule")
							break
						}
					}
				}
			}
		}

		if productAllowed {
			eligibleOrderAmount += cartItem.Price
			r.logger.WithContext(ctx).Debugf("Product allowed, adding price: %f, total: %f", cartItem.Price, eligibleOrderAmount)
		} else {
			r.logger.WithContext(ctx).Debugf("Product NOT allowed, skipping price: %f", cartItem.Price)
		}
	}

	r.logger.WithContext(ctx).Debugf("Final eligible order amount: %f", eligibleOrderAmount)
	return eligibleOrderAmount, nil
}

func (r *voucherRepository) convertUserTypeToString(userType int32) string {
	switch userType {
	case 0:
		return "NEW"
	case 1:
		return "VIP"
	default:
		return "NEW"
	}
}

func (r *voucherRepository) GetUserEligibilityRules(ctx context.Context, voucherID uint64) ([]*model.VoucherUserEligibility, error) {
	var rules []*model.VoucherUserEligibility
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucherID).Find(&rules).Error
	return rules, err
}

func (r *voucherRepository) ReplaceProductRestrictions(ctx context.Context, voucherID uint64, restrictions []*model.VoucherProductRestriction) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceProductRestrictionsInTx(ctx, tx, voucherID, restrictions)
	})
}

func (r *voucherRepository) ReplaceTimeRestrictions(ctx context.Context, voucherID uint64, restrictions []*model.VoucherTimeRestriction) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceTimeRestrictionsInTx(ctx, tx, voucherID, restrictions)
	})
}

func (r *voucherRepository) ReplaceUserEligibility(ctx context.Context, voucherID uint64, rules []*model.VoucherUserEligibility) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceUserEligibilityInTx(ctx, tx, voucherID, rules)
	})
}

func (r *voucherRepository) calculateVoucherStatistics(ctx context.Context, voucher *model.Voucher) error {
	if r.orderClient == nil {
		r.logger.WithContext(ctx).Debug("Order client not available, skipping voucher statistics calculation")
		return nil
	}

	orders, err := r.getVoucherOrders(ctx, voucher.ID)
	if err != nil {
		return fmt.Errorf("failed to get voucher orders: %w", err)
	}

	totalSavings := 0.0
	uniqueUserMap := make(map[uint64]bool)

	for _, order := range orders {
		discountAmount := r.calculateDiscountAmount(voucher, order.OrderAmount)
		totalSavings += discountAmount
		uniqueUserMap[order.UserID] = true
	}

	voucher.TotalSavings = totalSavings
	voucher.UniqueUsers = len(uniqueUserMap)

	return nil
}

type VoucherOrder struct {
	ID          uint64  `gorm:"column:id"`
	UserID      uint64  `gorm:"column:user_id"`
	OrderAmount float64 `gorm:"column:order_amount"`
	CreatedAt   string  `gorm:"column:created_at"`
}

func (r *voucherRepository) getVoucherOrders(ctx context.Context, voucherID uint64) ([]*VoucherOrder, error) {
	log := r.logger.WithContext(ctx)

	ordersResp, err := r.orderClient.ListOrdersByVoucher(ctx, voucherID, 1, 1000) // Get up to 1000 orders
	if err != nil {
		log.Errorf("Failed to get orders from order service for voucher %d: %v", voucherID, err)
		return nil, err
	}

	var voucherOrders []*VoucherOrder
	for _, order := range ordersResp.Data {
		voucherOrder := &VoucherOrder{
			ID:          order.ID,
			UserID:      order.UserID,
			OrderAmount: order.OrderAmount,
			CreatedAt:   order.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		}
		voucherOrders = append(voucherOrders, voucherOrder)
		log.Debugf("  Order: ID=%d, UserID=%d, Amount=%.2f", order.ID, order.UserID, order.OrderAmount)
	}

	return voucherOrders, nil
}

func (r *voucherRepository) loadUserUsageDetails(ctx context.Context, voucher *model.Voucher) error {
	log := r.logger.WithContext(ctx)

	if r.orderClient == nil || r.userClient == nil {
		log.Debug("Order or user client not available, skipping user usage details")
		return nil
	}

	orders, err := r.getVoucherOrders(ctx, voucher.ID)
	if err != nil {
		return fmt.Errorf("failed to get voucher orders: %w", err)
	}

	if len(orders) == 0 {
		log.Debugf("No orders found for voucher %d, no user usage to load", voucher.ID)
		return nil
	}

	userOrderMap := make(map[uint64][]*VoucherOrder)
	for _, order := range orders {
		userOrderMap[order.UserID] = append(userOrderMap[order.UserID], order)
	}

	var userUsageList []*model.UserVoucherUsage

	for userID, userOrders := range userOrderMap {
		user, err := r.userClient.GetUser(ctx, userID)
		if err != nil {
			log.Warnf("Failed to get user details for user %d: %v", userID, err)
			continue
		}

		var orderUsages []*model.VoucherOrderUsage
		for _, order := range userOrders {
			usedAt, err := time.Parse("2006-01-02T15:04:05Z07:00", order.CreatedAt)
			if err != nil {
				log.Warnf("Failed to parse order created_at time: %v", err)
				usedAt = time.Now()
			}

			orderUsage := &model.VoucherOrderUsage{
				OrderID:     fmt.Sprintf("%d", order.ID),
				UsedAt:      usedAt,
				OrderAmount: order.OrderAmount,
				Status:      "SUCCESS",
			}
			orderUsages = append(orderUsages, orderUsage)
		}

		var userTypeStr string
		if user.Type != 0 {
			userTypeStr = user.Type.String()
		}

		userUsage := &model.UserVoucherUsage{
			UserID:     userID,
			UsageCount: len(userOrders),
			FullName:   user.Name,
			Email:      user.Email,
			Type:       userTypeStr,
			Orders:     orderUsages,
		}

		userUsageList = append(userUsageList, userUsage)
	}

	voucher.UserUsage = userUsageList

	return nil
}

func (r *voucherRepository) calculateDiscountAmount(voucher *model.Voucher, orderAmount float64) float64 {
	if voucher.DiscountType == nil {
		return voucher.DiscountValue
	}

	switch voucher.DiscountType.TypeCode {
	case "PERCENT":
		discount := orderAmount * (voucher.DiscountValue / 100)
		if voucher.MaxDiscountAmount != nil && discount > *voucher.MaxDiscountAmount {
			discount = *voucher.MaxDiscountAmount
		}
		return discount
	case "FIXED":
		return voucher.DiscountValue
	default:
		return voucher.DiscountValue
	}
}

func (r *voucherRepository) replaceProductRestrictionsInTx(_ context.Context, tx *gorm.DB, voucherID uint64, restrictions []*model.VoucherProductRestriction) error {
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherProductRestriction{}).Error; err != nil {
		return err
	}

	for _, restriction := range restrictions {
		restriction.VoucherID = voucherID
		if err := tx.Create(restriction).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) replaceTimeRestrictionsInTx(_ context.Context, tx *gorm.DB, voucherID uint64, restrictions []*model.VoucherTimeRestriction) error {
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherTimeRestriction{}).Error; err != nil {
		return err
	}

	for _, restriction := range restrictions {
		restriction.VoucherID = voucherID
		if err := tx.Create(restriction).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) replaceUserEligibilityInTx(_ context.Context, tx *gorm.DB, voucherID uint64, rules []*model.VoucherUserEligibility) error {
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherUserEligibility{}).Error; err != nil {
		return err
	}

	for _, rule := range rules {
		rule.VoucherID = voucherID
		if err := tx.Create(rule).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) calculateDiscount(voucher *model.Voucher, orderAmount float64) float64 {
	var discount float64

	if voucher.DiscountType != nil {
		switch voucher.DiscountType.TypeCode {
		case "PERCENT":
			discount = orderAmount * (voucher.DiscountValue / 100)
		case "FIXED":
			discount = voucher.DiscountValue
		case "FLAT":
			discount = orderAmount - voucher.DiscountValue
			if discount < 0 {
				discount = orderAmount
			}
		default:
			discount = 0
		}
	} else {
		discount = orderAmount * (voucher.DiscountValue / 100)
	}

	if voucher.MaxDiscountAmount != nil && discount > *voucher.MaxDiscountAmount {
		discount = *voucher.MaxDiscountAmount
	}

	if discount > orderAmount {
		discount = orderAmount
	}

	discount = float64(int(discount*100+0.5)) / 100

	return discount
}
