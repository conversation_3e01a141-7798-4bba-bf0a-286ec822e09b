services:
  postgres-voucher:
    image: postgres:16-alpine
    container_name: postgres-voucher
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-voucher_db}
    ports:
      - "5435:5432"
    volumes:
      - postgres-voucher-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-voucher_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-voucher:
    image: redis:7-alpine
    container_name: redis-voucher
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6382:6379"
    volumes:
      - redis-voucher-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  voucher-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: voucher-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-voucher-service
    depends_on:
      postgres-voucher:
        condition: service_healthy
      redis-voucher:
        condition: service_healthy
    env_file:
      - .env
    ports:
      - "8083:8080"
      - "50054:50051"
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - coupon-network

volumes:
  postgres-voucher-data:
  redis-voucher-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
