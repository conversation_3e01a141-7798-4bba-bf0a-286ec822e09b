package database

import (
	"context"
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/sirupsen/logrus"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type DB struct {
	*gorm.DB
	config  *config.DatabaseConfig
	logger  *logging.Logger
	metrics *metrics.Metrics
}

func NewPostgresDB(cfg *config.DatabaseConfig, logger *logging.Logger, metrics *metrics.Metrics, serviceName string) (*DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s",
		cfg.Host, cfg.User, cfg.Password, cfg.Name, cfg.Port, cfg.SSLMode)

	gormConfig := &gorm.Config{
		Logger: &gormLogger{logger: logger, metrics: metrics, serviceName: serviceName},
	}

	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.MaxLifetime)

	return &DB{
		DB:      db,
		config:  cfg,
		logger:  logger,
		metrics: metrics,
	}, nil
}

func (db *DB) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

func (db *DB) Health(ctx context.Context) error {
	healthCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.PingContext(healthCtx)
}

type gormLogger struct {
	logger      *logging.Logger
	metrics     *metrics.Metrics
	serviceName string
}

func (l *gormLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

func (l *gormLogger) Info(ctx context.Context, msg string, data ...any) {
	l.logger.WithContext(ctx).Infof(msg, data...)
}

func (l *gormLogger) Warn(ctx context.Context, msg string, data ...any) {
	l.logger.WithContext(ctx).Warnf(msg, data...)
}

func (l *gormLogger) Error(ctx context.Context, msg string, data ...any) {
	l.logger.WithContext(ctx).Errorf(msg, data...)
}

func (l *gormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()

	status := "success"
	if err != nil {
		status = "error"
	}

	l.metrics.RecordDatabaseQuery(l.serviceName, "query", status, elapsed)

	fields := logrus.Fields{
		"sql":      sql,
		"rows":     rows,
		"duration": elapsed.String(),
	}

	if err != nil {
		fields["error"] = err.Error()
		l.logger.WithContext(ctx).WithFields(fields).Error("Database query failed")
	} else {
		l.logger.WithContext(ctx).WithFields(fields).Debug("Database query executed")
	}
}
