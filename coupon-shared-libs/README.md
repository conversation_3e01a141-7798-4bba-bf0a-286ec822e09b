# Coupon Shared Libraries

A comprehensive collection of reusable Go packages for the coupon microservice system. These libraries provide common functionality across all services including configuration management, database utilities, authentication, messaging, observability, and more.

## 🎯 Overview

The Coupon Shared Libraries provide:

- **Standardized Configuration**: Consistent configuration loading across all services
- **Database Utilities**: PostgreSQL and Redis helpers with connection pooling
- **Authentication**: JWT utilities and gRPC authentication middleware
- **Messaging**: Kafka producer and consumer utilities
- **Observability**: Logging, metrics, and distributed tracing
- **gRPC Utilities**: Client and server setup with middleware
- **Health Checks**: Standardized health check endpoints
- **Error Handling**: Consistent error handling and gRPC status codes

## 📦 Package Structure

### Core Packages

#### **auth** - Authentication Utilities

- JWT token generation and validation
- gRPC authentication middleware
- Service-to-service authentication helpers
- Cookie-based authentication support

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"

// JWT token validation
claims, err := auth.ValidateJWT(token, secretKey)

// gRPC auth middleware
server := grpc.NewServer(
    grpc.UnaryInterceptor(auth.UnaryServerInterceptor()),
)
```

#### **config** - Configuration Management

- Viper-based configuration loading
- Environment variable expansion
- YAML configuration file support
- Validation and type safety

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"

type ServiceConfig struct {
    Port     int    `mapstructure:"port"`
    Database DatabaseConfig `mapstructure:"database"`
}

cfg, err := config.Load[ServiceConfig]("config/config.yaml")
```

#### **database** - Database Utilities

- PostgreSQL connection management with GORM
- Connection pooling and health checks
- Auto-migration helpers
- Transaction utilities

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"

db, err := database.NewPostgresDB(config.Database)
defer database.Close(db)

// Auto-migration
err = database.AutoMigrate(db, &User{}, &Order{})
```

#### **redis** - Redis Client Wrapper

- Redis client with connection pooling
- Instrumentation and metrics
- Health check integration
- Caching utilities

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"

client, err := redis.NewClient(config.Redis)
defer redis.Close(client)

// Caching with TTL
err = redis.Set(client, "key", value, time.Hour)
```

#### **kafka** - Messaging Utilities

- Kafka producer and consumer setup
- Message serialization/deserialization
- Error handling and retry logic
- Health check integration

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"

// Producer
producer, err := kafka.NewProducer(config.Kafka)
err = kafka.PublishEvent(producer, "topic", event)

// Consumer
consumer, err := kafka.NewConsumer(config.Kafka, "group-id")
kafka.ConsumeEvents(consumer, "topic", handler)
```

#### **grpc** - gRPC Utilities

- gRPC server and client setup
- Middleware integration
- Connection management
- Health check support

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"

// Server setup
server := grpc.NewServer(
    grpc.WithAuth(),
    grpc.WithMetrics(),
    grpc.WithTracing(),
)

// Client setup
conn, err := grpc.NewClientConn("service:50051")
client := pb.NewServiceClient(conn)
```

#### **logging** - Structured Logging

- Structured JSON logging
- Tracing context integration
- Log level management
- Request/response logging

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"

logger := logging.NewLogger("service-name")
logger.Info("Service started", "port", 8080)
logger.Error("Database error", "error", err)
```

#### **metrics** - Prometheus Metrics

- Prometheus metrics collectors
- gRPC metrics middleware
- Database metrics
- Custom business metrics

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"

// Initialize metrics
metrics.Init("service-name")

// Custom metrics
counter := metrics.NewCounter("requests_total", "Total requests")
counter.Inc()
```

#### **tracing** - Distributed Tracing

- Jaeger tracer initialization
- Span creation and management
- gRPC tracing middleware
- Database query tracing

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"

tracer, closer, err := tracing.NewJaegerTracer("service-name")
defer closer.Close()

span := tracer.StartSpan("operation")
defer span.Finish()
```

#### **health** - Health Checks

- Echo-based health check handler
- Database health checks
- Redis health checks
- Kafka health checks

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"

checker := health.NewChecker()
checker.AddCheck("database", health.DatabaseCheck(db))
checker.AddCheck("redis", health.RedisCheck(redis))

e.GET("/health", health.Handler(checker))
```

#### **errors** - Error Handling

- gRPC status code utilities
- Error wrapping and unwrapping
- Consistent error responses
- Error logging integration

```go
import "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"

// Convert to gRPC error
err = errors.NotFound("user not found")
err = errors.InvalidArgument("invalid email format")

// Error handling middleware
server := grpc.NewServer(
    grpc.UnaryInterceptor(errors.UnaryServerInterceptor()),
)
```

## 🚀 Getting Started

### Installation

To include the shared libraries in your microservice:

```bash
go get gitlab.zalopay.vn/phunn4/coupon-shared-libs
```

### Requirements

- Go 1.24 or newer
- PostgreSQL 13+
- Redis 6+
- Kafka 2.8+

### Basic Usage

Here's a typical service setup using the shared libraries:

```go
package main

import (
    "context"
    "log"

    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
)

type ServiceConfig struct {
    Service  ServiceInfo    `mapstructure:"service"`
    Database DatabaseConfig `mapstructure:"database"`
    GRPC     GRPCConfig     `mapstructure:"grpc"`
}

func main() {
    // Load configuration
    cfg, err := config.Load[ServiceConfig]("config/config.yaml")
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // Initialize logging
    logger := logging.NewLogger(cfg.Service.Name)

    // Initialize tracing
    tracer, closer, err := tracing.NewJaegerTracer(cfg.Service.Name)
    if err != nil {
        logger.Fatal("Failed to initialize tracer", "error", err)
    }
    defer closer.Close()

    // Initialize metrics
    metrics.Init(cfg.Service.Name)

    // Initialize database
    db, err := database.NewPostgresDB(cfg.Database)
    if err != nil {
        logger.Fatal("Failed to connect to database", "error", err)
    }
    defer database.Close(db)

    // Initialize gRPC server
    server := grpc.NewServer(
        grpc.WithAuth(),
        grpc.WithMetrics(),
        grpc.WithTracing(),
    )

    // Register services
    pb.RegisterYourServiceServer(server, &yourService{})

    // Start server
    logger.Info("Starting gRPC server", "port", cfg.GRPC.Port)
    if err := grpc.Serve(server, cfg.GRPC.Port); err != nil {
        logger.Fatal("Failed to serve", "error", err)
    }
}
```

## 🔧 Development

### Makefile Commands

The shared libraries include a comprehensive Makefile for development:

```bash
# Build all packages
make build

# Run unit tests
make test

# Run tests with coverage
make test-coverage

# Run linting
make lint

# Format code
make fmt

# Run go vet
make vet

# Clean build artifacts
make clean

# Generate protobuf code
make proto

# Download dependencies
make deps

# Tidy module dependencies
make tidy

# Run all checks (test, lint, vet)
make check
```

### Testing

Each package includes comprehensive unit tests:

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests for specific package
go test ./auth/...
```

### Adding New Packages

When adding new shared functionality:

1. Create a new package directory
2. Implement the functionality with proper interfaces
3. Add comprehensive unit tests
4. Update this README with usage examples
5. Add the package to the Makefile build targets

## 📊 Observability Integration

### Metrics Collection

All packages automatically collect relevant metrics:

- **Database**: Connection pool stats, query duration, error rates
- **Redis**: Connection stats, operation duration, cache hit/miss rates
- **Kafka**: Producer/consumer metrics, message processing rates
- **gRPC**: Request counts, response times, error rates
- **HTTP**: Request duration, status code distributions

### Distributed Tracing

Automatic tracing integration across all components:

- Database queries are traced with query details
- Redis operations include cache hit/miss information
- Kafka messages include producer/consumer spans
- gRPC calls are traced end-to-end
- HTTP requests include full request/response context

### Structured Logging

Consistent logging format across all services:

```json
{
  "timestamp": "2023-12-01T10:00:00Z",
  "level": "info",
  "service": "user-service",
  "trace_id": "abc123",
  "span_id": "def456",
  "message": "User created successfully",
  "user_id": 123,
  "email": "<EMAIL>"
}
```

## 🔒 Security Features

### Authentication

- JWT token validation with configurable algorithms
- Service-to-service authentication with client credentials
- Cookie-based authentication support
- Token expiration and refresh handling

### Authorization

- Role-based access control utilities
- Permission checking middleware
- Resource-level authorization helpers

### Security Headers

- Automatic security header injection
- CORS configuration utilities
- Rate limiting helpers

## 🚨 Error Handling

### Consistent Error Responses

All packages use standardized error handling:

```go
// Business logic errors
err := errors.NotFound("user not found")
err := errors.InvalidArgument("invalid email format")
err := errors.PermissionDenied("insufficient permissions")

// System errors
err := errors.Internal("database connection failed")
err := errors.Unavailable("service temporarily unavailable")
```

### Error Propagation

Errors are properly propagated with context:

```go
// Wrap errors with additional context
err = errors.Wrap(err, "failed to create user")

// Add structured fields
err = errors.WithField(err, "user_id", userID)
err = errors.WithFields(err, map[string]interface{}{
    "operation": "create_user",
    "email": email,
})
```

## 📚 Best Practices

### Configuration Management

- Use environment variables for secrets
- Validate configuration on startup
- Provide sensible defaults
- Document all configuration options

### Database Operations

- Use transactions for multi-step operations
- Implement proper connection pooling
- Add database health checks
- Use prepared statements for security

### Messaging

- Implement idempotent message handlers
- Use dead letter queues for failed messages
- Add proper error handling and retries
- Monitor message processing metrics

### Observability

- Add structured logging to all operations
- Include trace context in all logs
- Collect business metrics, not just technical ones
- Set up proper alerting on key metrics

## 🤝 Contributing

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run `make check` to ensure quality
5. Submit a pull request

### Code Standards

- Follow Go best practices and idioms
- Write comprehensive unit tests (>80% coverage)
- Include proper documentation and examples
- Use consistent error handling patterns
- Add appropriate logging and metrics

### Package Guidelines

- Keep packages focused and cohesive
- Provide clear, documented interfaces
- Include usage examples in README
- Maintain backward compatibility when possible
- Follow semantic versioning for releases

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
