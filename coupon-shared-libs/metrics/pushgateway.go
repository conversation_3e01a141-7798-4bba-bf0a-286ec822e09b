package metrics

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/push"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type PushgatewayClient struct {
	pusher      *push.Pusher
	registry    *prometheus.Registry
	config      *config.MetricsConfig
	logger      *logging.Logger
	serviceName string
	instanceID  string
	stopCh      chan struct{}
}

func NewPushgatewayClient(cfg *config.MetricsConfig, serviceName string, logger *logging.Logger, registry *prometheus.Registry) (*PushgatewayClient, error) {
	if cfg.PushgatewayURL == "" {
		logger.Info("Pushgateway URL not configured, skipping pushgateway client initialization")
		return nil, nil
	}

	instanceID := cfg.InstanceID
	if instanceID == "" {
		hostname, err := os.Hostname()
		if err != nil {
			instanceID = fmt.Sprintf("%s-%d", serviceName, time.Now().Unix())
		} else {
			instanceID = fmt.Sprintf("%s-%s", serviceName, hostname)
		}
	}

	pusher := push.New(cfg.PushgatewayURL, "microservice_metrics").
		Gatherer(registry).
		Grouping("service_name", serviceName).
		Grouping("instance", instanceID)

	client := &PushgatewayClient{
		pusher:      pusher,
		registry:    registry,
		config:      cfg,
		logger:      logger,
		serviceName: serviceName,
		instanceID:  instanceID,
		stopCh:      make(chan struct{}),
	}

	return client, nil
}

func (p *PushgatewayClient) Start(ctx context.Context) {
	if p == nil || !p.config.Enabled {
		return
	}

	p.logger.Infof("Starting pushgateway client for service %s (instance: %s)", p.serviceName, p.instanceID)
	p.logger.Infof("Pushing metrics to %s every %v", p.config.PushgatewayURL, p.config.PushInterval)

	ticker := time.NewTicker(p.config.PushInterval)
	defer ticker.Stop()

	if err := p.pushMetrics(); err != nil {
		p.logger.Errorf("Initial metrics push failed: %v", err)
	}

	for {
		select {
		case <-ticker.C:
			if err := p.pushMetrics(); err != nil {
				p.logger.Errorf("Failed to push metrics: %v", err)
			}
		case <-ctx.Done():
			p.logger.Info("Stopping pushgateway client due to context cancellation")
			return
		case <-p.stopCh:
			p.logger.Info("Stopping pushgateway client")
			return
		}
	}
}

func (p *PushgatewayClient) Stop() {
	if p == nil {
		return
	}
	close(p.stopCh)
}

func (p *PushgatewayClient) pushMetrics() error {
	if p == nil || p.pusher == nil {
		return nil
	}

	start := time.Now()
	err := p.pusher.Push()
	duration := time.Since(start)

	if err != nil {
		p.logger.Errorf("Failed to push metrics to pushgateway (took %v): %v", duration, err)
		return err
	}

	p.logger.Debugf("Successfully pushed metrics to pushgateway (took %v)", duration)
	return nil
}

func (p *PushgatewayClient) PushOnce() error {
	if p == nil {
		return nil
	}
	return p.pushMetrics()
}

func (p *PushgatewayClient) AddCustomLabels(labels map[string]string) {
	if p == nil || p.pusher == nil {
		return
	}

	for key, value := range labels {
		p.pusher = p.pusher.Grouping(key, value)
	}
}

func (p *PushgatewayClient) GetInstanceID() string {
	if p == nil {
		return ""
	}
	return p.instanceID
}

func (p *PushgatewayClient) GetServiceName() string {
	if p == nil {
		return ""
	}
	return p.serviceName
}

func (p *PushgatewayClient) HealthCheck() error {
	if p == nil || p.config.PushgatewayURL == "" {
		return nil
	}

	testRegistry := prometheus.NewRegistry()
	testGauge := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "pushgateway_health_check",
		Help: "Health check metric for pushgateway connectivity",
	})
	testGauge.Set(1)
	testRegistry.MustRegister(testGauge)

	testPusher := push.New(p.config.PushgatewayURL, "health_check").
		Gatherer(testRegistry).
		Grouping("service_name", p.serviceName).
		Grouping("instance", p.instanceID).
		Grouping("check", "health")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	done := make(chan error, 1)
	go func() {
		done <- testPusher.Push()
	}()

	select {
	case err := <-done:
		if err != nil {
			return fmt.Errorf("pushgateway health check failed: %w", err)
		}
		return nil
	case <-ctx.Done():
		return fmt.Errorf("pushgateway health check timed out")
	}
}

func (p *PushgatewayClient) GetPushgatewayInfo() map[string]any {
	if p == nil {
		return map[string]any{
			"enabled":    false,
			"configured": false,
		}
	}

	return map[string]any{
		"enabled":       p.config.Enabled,
		"configured":    p.config.PushgatewayURL != "",
		"url":           p.config.PushgatewayURL,
		"push_interval": p.config.PushInterval.String(),
		"service_name":  p.serviceName,
		"instance_id":   p.instanceID,
	}
}
