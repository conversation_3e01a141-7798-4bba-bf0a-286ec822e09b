package metrics

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
)

type Manager struct {
	Metrics           *Metrics
	PushgatewayClient *PushgatewayClient
	Tracer            *tracing.Tracer
	logger            *logging.Logger
	serviceName       string
}

func NewManager(cfg *config.Config, serviceName string, logger *logging.Logger) (*Manager, error) {
	metrics := New(serviceName)

	var pushgatewayClient *PushgatewayClient
	var err error

	if cfg.Metrics.Enabled && cfg.Metrics.PushgatewayURL != "" {
		pushgatewayClient, err = NewPushgatewayClient(&cfg.Metrics, serviceName, logger, metrics.GetRegistry())
		if err != nil {
			logger.Errorf("Failed to create pushgateway client: %v", err)
			pushgatewayClient = nil
		}
	}

	var tracer *tracing.Tracer
	tracer, err = tracing.New(serviceName, cfg.Jaeger.Host, cfg.Jaeger.Port, cfg.Jaeger.Endpoint)
	if err != nil {
		logger.Errorf("Failed to create tracer: %v", err)
		tracer = nil
	}

	manager := &Manager{
		Metrics:           metrics,
		PushgatewayClient: pushgatewayClient,
		Tracer:            tracer,
		logger:            logger,
		serviceName:       serviceName,
	}

	return manager, nil
}

func (m *Manager) Start(ctx context.Context) {
	if m.PushgatewayClient != nil {
		m.logger.Infof("Starting metrics manager for service %s with pushgateway integration", m.serviceName)
		go m.PushgatewayClient.Start(ctx)
	} else {
		m.logger.Infof("Starting metrics manager for service %s (no pushgateway)", m.serviceName)
	}
}

func (m *Manager) Stop() {
	m.logger.Infof("Stopping metrics manager for service %s", m.serviceName)

	if m.PushgatewayClient != nil {
		if err := m.PushgatewayClient.PushOnce(); err != nil {
			m.logger.Errorf("Failed to push final metrics: %v", err)
		}
		m.PushgatewayClient.Stop()
	}

	if m.Tracer != nil {
		if err := m.Tracer.Close(); err != nil {
			m.logger.Errorf("Failed to close tracer: %v", err)
		}
	}
}

func (m *Manager) HealthCheck() map[string]any {
	health := map[string]any{
		"service_name":    m.serviceName,
		"metrics_enabled": true,
		"tracer_enabled":  m.Tracer != nil,
	}

	if m.PushgatewayClient != nil {
		pushgatewayHealth := m.PushgatewayClient.GetPushgatewayInfo()
		health["pushgateway"] = pushgatewayHealth

		if err := m.PushgatewayClient.HealthCheck(); err != nil {
			health["pushgateway_health"] = fmt.Sprintf("unhealthy: %v", err)
		} else {
			health["pushgateway_health"] = "healthy"
		}
	} else {
		health["pushgateway"] = map[string]any{
			"enabled":    false,
			"configured": false,
		}
	}

	return health
}

func (m *Manager) GetMetricsInfo() map[string]any {
	info := map[string]any{
		"service_name":    m.serviceName,
		"metrics_enabled": true,
		"tracer_enabled":  m.Tracer != nil,
	}

	if m.PushgatewayClient != nil {
		info["pushgateway"] = m.PushgatewayClient.GetPushgatewayInfo()
	}

	return info
}

func (m *Manager) PushMetricsOnce() error {
	if m.PushgatewayClient == nil {
		return fmt.Errorf("pushgateway client not configured")
	}
	return m.PushgatewayClient.PushOnce()
}

func (m *Manager) SetCustomLabels(labels map[string]string) {
	if m.PushgatewayClient != nil {
		m.PushgatewayClient.AddCustomLabels(labels)
	}
}

func (m *Manager) GetServiceName() string {
	return m.serviceName
}

func (m *Manager) GetInstanceID() string {
	if m.PushgatewayClient != nil {
		return m.PushgatewayClient.GetInstanceID()
	}
	return ""
}

func GetDefaultMetricsConfig() config.MetricsConfig {
	return config.MetricsConfig{
		Enabled:        true,
		PushgatewayURL: "",
		PushInterval:   30 * time.Second,
		InstanceID:     "",
	}
}

func ValidateMetricsConfig(cfg *config.MetricsConfig) error {
	if cfg.Enabled && cfg.PushgatewayURL != "" {
		if cfg.PushInterval <= 0 {
			return fmt.Errorf("push_interval must be positive when pushgateway is enabled")
		}
		if cfg.PushInterval < 5*time.Second {
			return fmt.Errorf("push_interval should be at least 5 seconds to avoid overwhelming the pushgateway")
		}
	}
	return nil
}
