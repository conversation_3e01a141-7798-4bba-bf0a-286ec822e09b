# Coupon Product Service

A comprehensive product and category management service for the coupon microservice system, built with Go and gRPC. This service handles product catalog management, category organization, and provides high-performance caching with Redis.

## 🎯 Service Overview

The Coupon Product Service provides:

- **Product Management**: CRUD operations for product catalog
- **Category Management**: Hierarchical category organization
- **High-Performance Caching**: Redis caching for improved response times
- **Search & Filtering**: Advanced product search and filtering capabilities
- **Stock Management**: Product inventory tracking
- **Health Monitoring**: Comprehensive health checks and metrics

### Key Features

- **Product CRUD Operations**: Complete product lifecycle management
- **Category Hierarchy**: Support for nested product categories
- **Redis Caching**: Intelligent caching strategy for products and categories
- **Advanced Filtering**: Search by name, category, price range, brand, status
- **Pagination Support**: Efficient pagination for large product catalogs
- **Stock Tracking**: Real-time inventory management
- **Database Integration**: PostgreSQL with GORM for data persistence and auto-migration
- **gRPC Authentication Middleware**: Service-to-service authentication
- **Distributed Tracing**: Jaeger integration for request tracing
- **Metrics Collection**: Prometheus metrics for monitoring and alerting

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│  Product Service │───▶│   PostgreSQL    │
│                 │    │                  │    │                 │
│ - Product APIs  │    │ - Product CRUD   │    │ - Products      │
│ - Category APIs │    │ - Category Mgmt  │    │ - Categories    │
│ - Search/Filter │    │ - Search/Filter  │    │ - Relationships │
└─────────────────┘    │ - Cache Mgmt     │    └─────────────────┘
                       │ - Health Checks  │
┌─────────────────┐    └──────────────────┘    ┌─────────────────┐
│ Other Services  │                            │      Redis      │
│                 │◀───────────────────────────│                 │
│ - Voucher Svc   │    Product Data            │ - Product Cache │
│ - Order Service │    (product details,       │ - Category Cache│
│ - Frontend      │     categories)            │ - Search Cache  │
└─────────────────┘                            └─────────────────┘
```

## 📋 gRPC API Methods

### Product Management
- `GetProduct(GetProductRequest) → GetProductResponse` - Get product by ID
- `UpdateProduct(UpdateProductRequest) → UpdateProductResponse` - Update product details
- `ListProducts(ListProductsRequest) → ListProductsResponse` - List products with filtering and pagination

### Category Management
- `ListCategories(ListCategoriesRequest) → ListCategoriesResponse` - List all categories

### Health Check
- `HealthCheck(HealthCheckRequest) → HealthCheckResponse` - Service health status

## 🔧 Configuration

### Environment Variables

```bash
# Service Configuration
PRODUCT_SERVICE_CLIENT_ID=product-service-client-id
PRODUCT_SERVICE_CLIENT_KEY=product-service-client-secret

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=coupon_product
POSTGRES_SSLMODE=disable

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Auth Service Configuration
AUTH_SERVICE_ADDR=auth-service:50051

# Cache Configuration
CACHE_TTL=3600
CACHE_ENABLED=true

# Observability
JAEGER_HOST=jaeger
JAEGER_PORT=6831
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Metrics
METRICS_ENABLED=true
METRICS_PUSHGATEWAY_URL=http://pushgateway:9091
METRICS_PUSH_INTERVAL=30s
METRICS_INSTANCE_ID=product-service-1
```

## 🗄️ Database Schema

### Products Table
```sql
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category_id INTEGER REFERENCES categories(id),
    image_url TEXT,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    brand VARCHAR(100),
    sku VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Categories Table
```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id INTEGER REFERENCES categories(id),
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Product Status Values
- `ACTIVE` - Available for purchase
- `INACTIVE` - Not available for purchase
- `OUT_OF_STOCK` - Temporarily unavailable
- `DISCONTINUED` - No longer available

## 🚀 Getting Started

### Prerequisites
- Go >= 1.24
- PostgreSQL 13+
- Redis 6+
- Docker and Docker Compose

### Local Development
1. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Build and run**
   ```bash
   make build
   make run
   ```

3. **Access the service**
   - gRPC Server: `localhost:50051`
   - HTTP Health Check: `http://localhost:8080/health`
   - Metrics: `http://localhost:8080/metrics`

### Docker Deployment
```bash
# Start with all dependencies
make compose-up

# Stop services
make compose-down
```

## 🔄 Redis Caching Strategy

### Cache Keys
- `product:{id}` - Individual product cache
- `products:list:{hash}` - Product list cache (based on filter parameters)
- `categories:list` - All categories cache
- `category:{id}` - Individual category cache

### Cache TTL
- Products: 1 hour (3600 seconds)
- Categories: 4 hours (14400 seconds)
- Product lists: 30 minutes (1800 seconds)

### Cache Invalidation
- Product updates invalidate related caches
- Category updates invalidate category and related product caches
- Automatic TTL-based expiration

## 🧪 Testing

### Unit Tests
```bash
make test
```

### gRPC Testing
```bash
# Get product by ID
grpcurl -plaintext -d '{"product_id":1}' \
  localhost:50051 product.ProductService/GetProduct

# List products with filtering
grpcurl -plaintext -d '{"category_id":1,"status":"ACTIVE","limit":10,"offset":0}' \
  localhost:50051 product.ProductService/ListProducts

# Update product
grpcurl -plaintext -d '{"product_id":1,"name":"Updated Product","price":29.99}' \
  localhost:50051 product.ProductService/UpdateProduct

# List categories
grpcurl -plaintext localhost:50051 product.ProductService/ListCategories
```

## 📊 Monitoring

### Health Checks
```bash
curl http://localhost:8080/health
```

### Metrics
- Product retrieval rates and cache hit/miss ratios
- Category access patterns and cache performance
- Database connection pool stats and query performance
- Redis cache hit rates and connection stats
- gRPC request counts, response times, and error rates

## 🔧 Development

### Project Structure
```
coupon-product-service/
├── cmd/server/main.go              # Application entry point
├── internal/
│   ├── service/product_service.go  # Core business logic
│   ├── repository/product_repo.go  # Data access layer
│   ├── model/                      # Data models
│   │   ├── product.go
│   │   └── category.go
│   ├── cache/redis_cache.go        # Redis caching layer
│   └── middleware/grpc_auth.go     # Authentication middleware
├── config/config.yaml              # Service configuration
├── Dockerfile                      # Container image
├── docker-compose.yml              # Local development
└── Makefile                        # Build commands
```

## 🚨 Error Handling

### gRPC Error Codes
- `OK` - Successful operation
- `INVALID_ARGUMENT` - Invalid request parameters
- `NOT_FOUND` - Product or category not found
- `ALREADY_EXISTS` - Duplicate SKU or name
- `INTERNAL` - Internal server error
- `UNAVAILABLE` - Service temporarily unavailable

## 🔒 Security Features

- **Service Authentication**: gRPC middleware with client credentials
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: GORM ORM protection
- **Access Control**: Method-level access control
- **Audit Logging**: Product modification logging

## ⚡ Performance Optimization

### Caching Strategy
- **Read-Through Cache**: Automatic cache population on cache miss
- **Write-Through Cache**: Cache updates on data modification
- **Cache Warming**: Preload frequently accessed data
- **TTL Management**: Intelligent cache expiration

### Database Optimization
- **Indexing**: Optimized database indexes for common queries
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Optimized SQL queries with GORM

## 📚 Additional Resources

- [gRPC Go Documentation](https://grpc.io/docs/languages/go/)
- [GORM Documentation](https://gorm.io/docs/)
- [Redis Go Client](https://github.com/go-redis/redis)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## Development

### Prerequisites

- Go 1.24+
- PostgreSQL
- Redis
- Docker (optional)

### Building

```bash
make build
```

### Running

```bash
make run
```

### Testing

```bash
make test
```

### Docker

```bash
make docker-build
```

## Service Registration

The product service needs to be registered with the auth service to obtain client credentials:

```bash
# From the auth-service directory
make register-service SERVICE_NAME=product-service SERVICE_VERSION=1.0.0 DESCRIPTION="Product management service"
```

This will generate the `PRODUCT_SERVICE_CLIENT_ID` and `PRODUCT_SERVICE_CLIENT_KEY` values.

## Integration

The product service integrates with:

- **API Gateway**: HTTP endpoints for external access
- **Auth Service**: Service authentication and authorization
- **Other Services**: Can be called by voucher-service, order-service, etc.

## Architecture

The service follows the established microservice patterns:

- **Handler Layer**: gRPC and HTTP request handling
- **Service Layer**: Business logic and validation
- **Repository Layer**: Data access and persistence
- **Model Layer**: Data structures and DTOs

## Caching Strategy

The service implements Redis caching for improved performance:

### Product Caching

- **Cache Key**: `product:id:{product_id}`
- **TTL**: 10 minutes
- **Operations**: Cached on read, invalidated on update

### Category Caching

- **Individual Categories**: `category:id:{category_id}` (TTL: 30 minutes)
- **Category Lists**: `categories:list` (TTL: 30 minutes, simple queries only)

### Cache Invalidation

- Product cache is invalidated when products are updated
- Category cache uses TTL-based expiration

## Monitoring

- **Health Check**: `GET /health` (includes database and Redis health)
- **Metrics**: `GET /metrics` (Prometheus format)
- **Tracing**: Jaeger integration
- **Logging**: Structured JSON logging
