.PHONY: help deploy start stop restart status logs health clean backup restore urls jaeger-health pushgateway-test start-services stop-services check-metrics benchmark-all benchmark-user benchmark-voucher benchmark-product benchmark-auth benchmark-api-gateway benchmark-order benchmark-notification benchmark-report benchmark-clean

TIMESTAMP := $(shell date +%Y%m%d_%H%M%S)
RESULTS_DIR := benchmark-results
BACKUP_DIR := monitoring-backups
SERVICES := user-service voucher-service product-service auth-service api-gateway order-service notification-service

include .env
export

help:
	@echo "Coupon Infrastructure Services - VPS Deployment"
	@echo "=============================================="
	@echo ""
	@echo "Main Commands:"
	@echo "  deploy         Deploy complete infrastructure stack to VPS"
	@echo "  start          Start all infrastructure services"
	@echo "  stop           Stop all infrastructure services"
	@echo "  restart        Restart all infrastructure services"
	@echo "  status         Show service status"
	@echo "  health         Run health checks on all services"
	@echo "  logs           Show logs (use: make logs SERVICE=prometheus)"
	@echo "  clean          Clean up all services and volumes"
	@echo ""
	@echo "Service Management:"
	@echo "  urls           Display all service URLs"
	@echo "  start-services Start local business services"
	@echo "  stop-services  Stop local business services"
	@echo "  check-metrics  Check dashboard metrics and diagnostics"
	@echo "  backup         Backup all persistent data"
	@echo "  restore        Restore from backup (use: make restore BACKUP_DATE=20231201_120000)"
	@echo ""
	@echo "Infrastructure Services:"
	@echo "  jaeger-health  Check Jaeger tracing health"
	@echo "  pushgateway-test Test pushgateway connectivity"
	@echo ""
	@echo "Benchmarking (Legacy):"
	@echo "  benchmark-all  Run all service benchmarks"
	@echo "  benchmark-*    Run specific service benchmarks"
	@echo ""

deploy:
	@echo "🚀 Deploying VPS infrastructure stack..."
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh

start:
	@echo "▶️  Starting infrastructure services..."
	@./scripts/deploy.sh start

stop:
	@echo "⏹️  Stopping infrastructure services..."
	@./scripts/deploy.sh stop

restart:
	@echo "🔄 Restarting infrastructure services..."
	@./scripts/deploy.sh restart

status:
	@echo "📊 Infrastructure services status:"
	@./scripts/deploy.sh status

health:
	@echo "🏥 Running infrastructure health checks..."
	@./scripts/deploy.sh health

logs:
	@if [ -z "$(SERVICE)" ]; then \
		echo "📋 Showing all service logs..."; \
		docker compose logs -f --tail=100; \
	else \
		echo "📋 Showing logs for $(SERVICE)..."; \
		docker compose logs -f --tail=100 $(SERVICE); \
	fi

clean:
	@echo "🧹 Cleaning up infrastructure stack..."
	@docker compose down -v
	@docker system prune -f
	@echo "✅ Infrastructure cleanup completed"

benchmark-all:
	@echo "Running benchmarks for all services..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh
	@echo "All benchmarks completed. Results in $(RESULTS_DIR)/"

benchmark-user:
	@echo "Running user service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@USER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "User service benchmarks completed"

benchmark-voucher:
	@echo "Running voucher service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@VOUCHER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Voucher service benchmarks completed"

benchmark-product:
	@echo "Running product service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@PRODUCT_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Product service benchmarks completed"

benchmark-auth:
	@echo "Running auth service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@AUTH_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Auth service benchmarks completed"

benchmark-api-gateway:
	@echo "Running API gateway benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@API_GATEWAY_ONLY=true ./scripts/run-benchmarks.sh
	@echo "API gateway benchmarks completed"

benchmark-order:
	@echo "Running order service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@ORDER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Order service benchmarks completed"

benchmark-notification:
	@echo "Running notification service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@NOTIFICATION_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
	@echo "Notification service benchmarks completed"

benchmark-load:
	@echo "Running load testing scenarios..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh load
	@echo "Load testing completed"

benchmark-stress:
	@echo "Running stress testing scenarios..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh stress
	@echo "Stress testing completed"

benchmark-report:
	@echo "Generating benchmark report..."
	@mkdir -p $(RESULTS_DIR)
	@echo "# Benchmark Report - $(TIMESTAMP)" > $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "## Summary" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "Generated: $(shell date)" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@for file in $(RESULTS_DIR)/*_$(TIMESTAMP).txt; do \
		if [ -f "$$file" ]; then \
			echo "### $$(basename $$file .txt)" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			tail -20 "$$file" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
		fi \
	done
	@echo "Benchmark report generated: $(RESULTS_DIR)/report_$(TIMESTAMP).md"

benchmark-clean:
	@echo "Cleaning up benchmark results..."
	@rm -rf $(RESULTS_DIR)
	@echo "Benchmark results cleaned"

backup:
	@echo "💾 Backing up infrastructure data..."
	@mkdir -p $(BACKUP_DIR)
	@docker run --rm -v prometheus-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/prometheus-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v grafana-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/grafana-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v alertmanager-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/alertmanager-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v kafka-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/kafka-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v zookeeper-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/zookeeper-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v zookeeper-logs:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/zookeeper-logs-$(TIMESTAMP).tar.gz /data
	@echo "✅ Infrastructure data backed up to $(BACKUP_DIR)/"
	@echo "📁 Backup files:"
	@ls -la $(BACKUP_DIR)/*$(TIMESTAMP)*

restore:
	@echo "📥 Restoring infrastructure data..."
	@if [ -z "$(BACKUP_DATE)" ]; then \
		echo "❌ Usage: make restore BACKUP_DATE=20231201_120000"; \
		echo "📁 Available backups:"; \
		ls -la $(BACKUP_DIR)/ | grep -E "prometheus-|grafana-|alertmanager-" | head -5; \
		exit 1; \
	fi
	@echo "⏹️  Stopping services..."
	@./scripts/deploy.sh stop
	@echo "📥 Restoring data from $(BACKUP_DATE)..."
	@docker run --rm -v prometheus-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/prometheus-$(BACKUP_DATE).tar.gz -C /
	@docker run --rm -v grafana-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/grafana-$(BACKUP_DATE).tar.gz -C /
	@docker run --rm -v alertmanager-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/alertmanager-$(BACKUP_DATE).tar.gz -C /
	@if [ -f "$(BACKUP_DIR)/kafka-$(BACKUP_DATE).tar.gz" ]; then \
		docker run --rm -v kafka-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/kafka-$(BACKUP_DATE).tar.gz -C /; \
	fi
	@if [ -f "$(BACKUP_DIR)/zookeeper-$(BACKUP_DATE).tar.gz" ]; then \
		docker run --rm -v zookeeper-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/zookeeper-$(BACKUP_DATE).tar.gz -C /; \
	fi
	@echo "▶️  Starting services..."
	@./scripts/deploy.sh start
	@echo "✅ Infrastructure data restored from $(BACKUP_DATE)"



jaeger-health:
	@echo "🔍 Jaeger Tracing Health Check:"
	@echo "==============================="
	@echo ""
	@echo "📊 Jaeger UI: http://0.0.0.0:16686"
	@echo "🔗 Collector HTTP: http://0.0.0.0:14268"
	@echo "🔗 Agent UDP: 0.0.0.0:6831, 0.0.0.0:6832"
	@echo ""
	@echo "🏥 Health status:"
	@curl -s http://localhost:16686/ > /dev/null && echo "✅ Jaeger UI is accessible" || echo "❌ Jaeger UI is not accessible"
	@curl -s http://localhost:14268/ > /dev/null && echo "✅ Jaeger Collector is accessible" || echo "❌ Jaeger Collector is not accessible"

pushgateway-test:
	@echo "📤 Pushgateway Connectivity Test:"
	@echo "================================="
	@echo ""
	@echo "🔗 Pushgateway URL: http://0.0.0.0:9091"
	@echo ""
	@echo "📊 Current metrics:"
	@curl -s http://localhost:9091/metrics | head -10 || echo "❌ Cannot connect to Pushgateway"
	@echo ""
	@echo "🧪 Testing metric push:"
	@echo "test_metric 42" | curl -s --data-binary @- http://localhost:9091/metrics/job/test/instance/makefile
	@echo "✅ Test metric pushed successfully"
	@echo ""
	@echo "💡 Local services should push metrics to: http://0.0.0.0:9091"

urls:
	@echo "🌐 VPS Infrastructure Service URLs:"
	@echo "=================================="
	@echo ""
	@echo "External Access (Public):"
	@echo "  Grafana:      http://0.0.0.0:3000 (admin/admin123"
	@echo "  Pushgateway:  http://0.0.0.0:9091"
	@echo "  Alertmanager: http://0.0.0.0:9093"
	@echo "  Jaeger UI:    http://0.0.0.0:16686"
	@echo "  Kafka UI:     http://0.0.0.0:8090"
	@echo ""
	@echo "Internal Access (VPS Only):"
	@echo "  Prometheus:   http://localhost:9090 (use Grafana instead)"
	@echo "  Node Exporter: http://localhost:9100 (metrics via Grafana)"
	@echo "  cAdvisor:     http://localhost:8080 (metrics via Grafana)"
	@echo "  Kafka:        localhost:9092"
	@echo "  Zookeeper:    localhost:2181"
	@echo ""
	@echo "💡 Tip: Use 'make health' to verify all services are running"

metrics-test:
	@echo "Testing metrics endpoints..."
	@for service in $(SERVICES); do \
		echo "Testing coupon-$$service metrics..."; \
		curl -s http://localhost:8080/metrics | head -5 || echo "Service not available"; \
		echo ""; \
	done

ci-benchmark:
	@echo "Running CI benchmark suite..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh quick
	@echo "CI benchmarks completed"

clean-all: clean benchmark-clean
	@echo "🧹 Cleaning up all infrastructure and benchmark data..."
	@rm -rf $(BACKUP_DIR)
	@echo "✅ Complete cleanup finished"

start-services:
	@echo "🚀 Starting local business services..."
	@chmod +x scripts/start-local-services.sh
	@./scripts/start-local-services.sh

stop-services:
	@echo "🛑 Stopping local business services..."
	@chmod +x scripts/stop-local-services.sh
	@./scripts/stop-local-services.sh

check-metrics:
	@echo "🔍 Checking dashboard metrics and diagnostics..."
	@chmod +x scripts/check-dashboard-metrics.sh
	@./scripts/check-dashboard-metrics.sh
