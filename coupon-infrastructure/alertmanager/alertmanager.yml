global:
  smtp_smarthost: "localhost:587"
  smtp_from: "<EMAIL>"
  smtp_auth_username: "<EMAIL>"
  smtp_auth_password: "password"

route:
  group_by: ["alertname", "cluster", "service"]
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: "web.hook"
  routes:
    - match:
        severity: critical
      receiver: "critical-alerts"
    - match:
        severity: warning
      receiver: "warning-alerts"
    - match:
        alertname: ServiceDown
      receiver: "service-down-alerts"

receivers:
  - name: "web.hook"
    webhook_configs:
      - url: "http://localhost:5001/webhook"
        send_resolved: true

  - name: "critical-alerts"
    email_configs:
      - to: "<EMAIL>"
    webhook_configs:
      - url: "http://localhost:5001/critical"
        send_resolved: true

  - name: "warning-alerts"
    email_configs:
      - to: "<EMAIL>"

  - name: "service-down-alerts"
    email_configs:
      - to: "<EMAIL>,<EMAIL>"
    webhook_configs:
      - url: "http://localhost:5001/service-down"
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: "critical"
    target_match:
      severity: "warning"
    equal: ["alertname", "service", "instance"]
