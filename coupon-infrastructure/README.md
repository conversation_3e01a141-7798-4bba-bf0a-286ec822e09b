# Coupon Infrastructure Services

VPS infrastructure services for the coupon microservice system

## 🏗️ Architecture Overview

This infrastructure stack runs on VPS and includes:

### Monitoring Stack

- **Prometheus** - Metrics collection and storage
- **Grafana** - Visualization dashboards and analytics
- **Alertmanager** - Alert routing and notification management
- **Node Exporter** - VPS system and hardware metrics
- **cAdvisor** - Container resource usage and performance metrics
- **Pushgateway** - Receives metrics from private IP services

### Infrastructure Services

- **Jaeger** - Distributed tracing for microservices
- **Kafka** (latest) - Message queue in KRaft mode (no Zookeeper needed)
- **Kafka-UI** - Web interface for Kafka management

## 📋 Prerequisites

### VPS Requirements

- **Ubuntu 20.04+** or similar Linux distribution
- **Docker** (v20.10+) and **Docker Compose** (v2.0+) installed
- **Public IP address** accessible from internet
- **Sufficient system resources**:
  - RAM: 8GB minimum (16GB recommended for production)
  - Disk: 50GB free space for metrics, logs, and Kafka storage
  - CPU: 4 cores minimum (8 cores recommended)
- **Network ports available**:
  - 3000 (Grafana UI)
  - 9090 (Prometheus - internal only)
  - 9091 (Pushgateway)
  - 9093 (Alertmanager)
  - 9100 (Node Exporter - internal only)
  - 8080 (cAdvisor - internal only)
  - 16686 (Jaeger UI)
  - 6831/6832 (Jaeger agent UDP)
  - 14268 (Jaeger collector HTTP)
  - 9092 (Kafka)
  - 29092 (Kafka internal)
  - 8090 (Kafka-UI)

### VPS Setup Instructions

1. **Install Docker**:

   ```bash
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   ```

2. **Install Docker Compose**:

   ```bash
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

3. **Configure Firewall** (if using UFW):
   ```bash
   sudo ufw allow 3000,9091,9093,16686,8090,9092/tcp
   sudo ufw enable
   ```

## 🚀 Quick Start

### 1. Deploy to VPS

```bash
# Copy infrastructure to VPS
scp -r . user@YOUR_VPS_IP:~/coupon-infrastructure/

# SSH to VPS and deploy
ssh user@YOUR_VPS_IP
cd ~/coupon-infrastructure
make deploy
```

### 2. Verify Deployment

```bash
# Check all services are running
make status

# Run health checks
make health

# View service URLs
make urls
```

**Note**: All services are configured for automatic setup:

- Grafana dashboards are auto-imported on startup
- Kafka topics are auto-created when services publish messages
- Prometheus data sources are auto-configured

### Individual Operations

```bash
# Start services
make start

# Stop services
make stop

# Restart services
make restart

# Check status
make status

# View logs
make logs SERVICE=prometheus

# Run health checks
make health
```

## 🔧 Configuration

### Prometheus Configuration

- **Config file**: `prometheus/prometheus.yml`
- **Scrape method**: Pushgateway only (no direct service scraping)
- **Retention**: 30 days (configurable via .env)
- **Monitored targets**:
  - Pushgateway (receives metrics from local services)
  - Node Exporter (VPS system metrics)
  - cAdvisor (VPS container metrics)
  - Prometheus itself

### Grafana Configuration

- **Default credentials**: admin/admin123 (configurable via .env)
- **Data source**: Prometheus (auto-configured on startup)
- **Dashboards**: Auto-imported from `grafana/dashboards/` directory
- **Provisioning**: Automatic via `grafana/provisioning/` (no manual import needed)
- **External access**: Available on port 3000

### Alertmanager Configuration

- **Config file**: `alertmanager/alertmanager.yml`
- **Email notifications**: Configurable via .env variables
- **Webhook support**: Available for external integrations
- **Alert routing**: Based on severity levels (critical, warning)

### Kafka Configuration

- **Mode**: KRaft mode (no Zookeeper required)
- **Broker**: Single broker setup for development
- **Topics**: Auto-created when services publish messages (no manual setup required)
- **UI**: Kafka-UI available on port 8090 for management and monitoring
- **Persistence**: Data stored in Docker volumes
- **Retention**: 7 days (168 hours) default retention period
- **Default Settings**: Auto-created topics use 1 partition and 1 replica by default

### Jaeger Configuration

- **All-in-one**: Single container for development
- **UI**: Available on port 16686
- **Collectors**: HTTP (14268) and UDP (6831/6832)
- **OTLP**: OpenTelemetry Protocol enabled

## 📊 Service URLs

After deployment on VPS, access the services at:

### External Access (Public)

| Service      | URL                 | Credentials    | Purpose               |
| ------------ | ------------------- | -------------- | --------------------- |
| Grafana      | http://VPS_IP:3000  | admin/admin123 | Monitoring dashboards |
| Pushgateway  | http://VPS_IP:9091  | -              | Metrics collection    |
| Alertmanager | http://VPS_IP:9093  | -              | Alert management      |
| Jaeger UI    | http://VPS_IP:16686 | -              | Distributed tracing   |
| Kafka UI     | http://VPS_IP:8090  | -              | Kafka management      |

### Internal Access (VPS Only)

| Service       | URL                   | Purpose                         |
| ------------- | --------------------- | ------------------------------- |
| Prometheus    | http://localhost:9090 | Metrics storage (use Grafana)   |
| Node Exporter | http://localhost:9100 | System metrics (use Grafana)    |
| cAdvisor      | http://localhost:8080 | Container metrics (use Grafana) |
| Kafka         | localhost:9092        | Message broker                  |

## 🔍 Monitoring Features

### Metrics Collection

- **Application metrics**: Custom business metrics from microservices
- **System metrics**: CPU, memory, disk, network via Node Exporter
- **Container metrics**: Docker container resource usage via cAdvisor
- **Service health**: HTTP endpoint monitoring and availability

### Alerting Rules

- **Service Down**: Triggers when services become unavailable
- **High CPU Usage**: Alerts on sustained high CPU utilization
- **Memory Usage**: Monitors memory consumption thresholds
- **Disk Space**: Warns when disk usage exceeds limits
- **Response Time**: Alerts on slow API response times

### Dashboards

- **System Overview**: High-level system health and performance
- **Service Metrics**: Individual microservice performance
- **Infrastructure**: Hardware and container resource usage
- **Alerts**: Current alert status and history

## 🛠️ Troubleshooting

### Common Issues

#### 1. Alertmanager Configuration Errors

**Problem**: YAML unmarshal errors with "field subject/body not found"
**Solution**: The configuration has been updated for Alertmanager v0.25.0 compatibility. Use `html` and `text` fields instead of `body`.

#### 2. Grafana Permission Issues

**Problem**: Grafana container fails to start due to permission errors
**Solution**: The deployment script automatically sets ownership to UID/GID 472:472:

```bash
sudo chown -R 472:472 "$MONITORING_DIR/grafana/data"
```

#### 3. Services Not Appearing in Prometheus

**Problem**: Microservices not showing up in Prometheus targets
**Solution**:

- Ensure services are running and exposing metrics on port 8080
- Check that services are on the `coupon-network` Docker network
- Verify service names match those in `prometheus.yml`

#### 4. Network Connectivity Issues

**Problem**: Services cannot communicate with each other
**Solution**: Ensure the `coupon-network` Docker network exists:

```bash
docker network create coupon-network
```

### Debugging Commands

```bash
# Check container status
docker compose ps

# View service logs
docker compose logs -f [service-name]

# Test Prometheus targets
curl http://localhost:9090/api/v1/targets

# Test Grafana health
curl http://localhost:3000/api/health

# Test Alertmanager
curl http://localhost:9093/-/healthy
```

## 🔒 Security Considerations

- **Default passwords**: Change Grafana admin password in production
- **Network isolation**: Services communicate via Docker network
- **Email credentials**: Update SMTP settings in `alertmanager.yml`
- **Webhook security**: Implement authentication for webhook endpoints

## 📈 Performance Tuning

### Prometheus Optimization

- **Storage retention**: Adjust `--storage.tsdb.retention.time` for your needs
- **Scrape intervals**: Balance between data granularity and resource usage
- **Memory usage**: Monitor Prometheus memory consumption

### Grafana Optimization

- **Dashboard queries**: Optimize PromQL queries for better performance
- **Refresh intervals**: Set appropriate dashboard refresh rates
- **Plugin management**: Only install necessary plugins

## 🔄 Backup and Recovery

### Backup Data

```bash
# Backup all monitoring data
make monitoring-backup
```

### Restore Data

```bash
# Restore from specific backup
make monitoring-restore BACKUP_DATE=20231201_120000
```

## 🧪 Testing and Benchmarking

The monitoring stack includes comprehensive benchmarking tools:

```bash
# Run all service benchmarks
make benchmark-all

# Run specific service benchmarks
make benchmark-user
make benchmark-voucher
make benchmark-product

# Generate benchmark reports
make benchmark-report
```

## 🤝 Integration with Microservices

### Metrics Endpoint Requirements

Each microservice should expose metrics at:

- **Path**: `/metrics`
- **Port**: `8080`
- **Format**: Prometheus format

### Custom Metrics

Services can expose custom business metrics:

```go
// Example Go metrics
var (
    requestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
)
```

## 📚 Additional Resources

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Alertmanager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review service logs using `make monitoring-logs`
3. Run health checks with `make monitoring-health`
4. Consult the official documentation for each component
