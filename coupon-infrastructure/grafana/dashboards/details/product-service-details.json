{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": ["production"], "targetBlank": true, "title": "Back to Overview", "tooltip": "", "type": "dashboards", "url": ""}], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Key metrics for Product Service", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Service Uptime"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Request Rate"}, "properties": [{"id": "unit", "value": "reqps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Avg Response Time"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "up{job=\"product-service\"} * 100", "interval": "", "legendFormat": "Service Uptime", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(grpc_server_handled_total{service=\"product-service\"}[5m]))", "interval": "", "legendFormat": "Request Rate", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(grpc_server_handled_total{service=\"product-service\", grpc_code!=\"OK\"}[5m])) / sum(rate(grpc_server_handled_total{service=\"product-service\"}[5m])) * 100", "interval": "", "legendFormat": "Error Rate", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.50, sum(rate(grpc_server_handling_seconds_bucket{service=\"product-service\"}[5m])) by (le)) * 1000", "interval": "", "legendFormat": "Avg Response Time", "refId": "D"}], "title": "Product Service - Key Metrics", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Product operations breakdown by type", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Operations/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum by(operation) (rate(business_operations_total{service=\"product-service\", operation=~\"product_.*\"}[5m]))", "interval": "", "legendFormat": "{{operation}}", "refId": "A"}], "title": "Product Operations by Type", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Cache performance for product service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Hit Rate %", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum by(cache_type) (rate(cache_hits_total{service=\"product-service\", result=\"hit\"}[5m])) / sum by(cache_type) (rate(cache_hits_total{service=\"product-service\"}[5m])) * 100", "interval": "", "legendFormat": "{{cache_type}} Cache Hit Rate", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Product search and category operations", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Operations/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"product-service\", operation=\"product_search\"}[5m]))", "interval": "", "legendFormat": "Product Search", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"product-service\", operation=\"category_list\"}[5m]))", "interval": "", "legendFormat": "Category Listing", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(business_operations_total{service=\"product-service\", operation=\"product_detail\"}[5m]))", "interval": "", "legendFormat": "Product Detail Views", "refId": "C"}], "title": "Search & Browse Operations", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "gRPC method performance breakdown", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Requests/sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum by(grpc_method) (rate(grpc_server_handled_total{service=\"product-service\"}[5m]))", "interval": "", "legendFormat": "{{grpc_method}}", "refId": "A"}], "title": "gRPC Methods Request Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Database query performance for product service", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Duration", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "id": 6, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.95, sum by(operation, le) (rate(database_query_duration_seconds_bucket{service=\"product-service\"}[5m])))", "interval": "", "legendFormat": "P95 - {{operation}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "histogram_quantile(0.50, sum by(operation, le) (rate(database_query_duration_seconds_bucket{service=\"product-service\"}[5m])))", "interval": "", "legendFormat": "P50 - {{operation}}", "refId": "B"}], "title": "Database Query Latency", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 38, "style": "dark", "tags": ["production", "product-service", "microservice"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Product Service - Detailed Metrics", "uid": "product-service-details", "version": 1, "weekStart": ""}