{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 3, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "panels": [], "title": "🖥️ VPS Infrastructure", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "VPS host system resources", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 1}, "id": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-***********", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\",job=\"node-exporter\"}[5m])) * 100)", "legendFormat": "CPU Usage", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "(1 - (avg(node_memory_MemAvailable_bytes{job=\"node-exporter\"}) / avg(node_memory_MemTotal_bytes{job=\"node-exporter\"}))) * 100", "legendFormat": "Memory Usage", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "(1 - (avg(node_filesystem_avail_bytes{mountpoint=\"/\",job=\"node-exporter\"}) / avg(node_filesystem_size_bytes{mountpoint=\"/\",job=\"node-exporter\"}))) * 100", "legendFormat": "Disk Usage", "refId": "C"}], "title": "VPS Host Resources", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Status of VPS infrastructure services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "Down"}, "1": {"color": "green", "index": 0, "text": "Running"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 1}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-***********", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "up{job=~\"prometheus|grafana|cadvisor|node-exporter|pushgateway|alertmanager\"}", "legendFormat": "{{job}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "max by (name) (up{container_name=~\"jaeger|kafka|kafka-ui\"}) or max by (name) ((container_last_seen{name=~\"jaeger|kafka|kafka-ui\"} > bool (time() - 300)) * 1)", "legendFormat": "{{name}}", "refId": "B"}], "title": "VPS Infrastructure Services", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "CPU usage of VPS infrastructure containers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-***********", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "max by (name) (rate(container_cpu_usage_seconds_total{name=~\"prometheus|grafana|cadvisor|pushgateway|alertmanager|jaeger|kafka|kafka-ui\"}[5m])) * 100", "legendFormat": "{{name}}", "refId": "A"}], "title": "VPS All Container CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Memory usage of VPS infrastructure containers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-***********", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "max by (name) (container_memory_usage_bytes{name=~\"prometheus|grafana|cadvisor|pushgateway|alertmanager|jaeger|kafka|kafka-ui\"})", "legendFormat": "{{name}}", "refId": "A"}], "title": "VPS All Container Memory Usage", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 11, "panels": [], "title": "🏢 Local Business Services", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Resource usage of local business services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 16}, "id": 12, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-***********", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(process_cpu_seconds_total{job=\"microservice-metrics\",service_name=~\"auth-service|user-service|product-service|voucher-service|order-service|notification-service|api-gateway\"}[5m])) * 100 or vector(0)", "legendFormat": "Avg CPU Usage %", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(process_resident_memory_bytes{job=\"microservice-metrics\",service_name=~\"auth-service|user-service|product-service|voucher-service|order-service|notification-service|api-gateway\"}) / 1024 / 1024 or vector(0)", "legendFormat": "Total Memory (MB)", "refId": "B"}], "title": "Local Host Resources", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Status of local business services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "index": 1, "text": "Down"}, "1": {"color": "green", "index": 0, "text": "Running"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-***********", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"auth-service\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"user-service\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"product-service\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"voucher-service\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"order-service\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"notification-service\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "label_replace(vector(0), \"service_name\", \"api-gateway\", \"\", \"\")", "legendFormat": "{{service_name}}", "refId": "G"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "up{job=\"microservice-metrics\",service_name=~\"auth-service|user-service|product-service|voucher-service|order-service|notification-service|api-gateway\"}", "legendFormat": "{{service_name}}", "refId": "H"}], "title": "Local Business Services", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "CPU usage of local business services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "rate(process_cpu_seconds_total{job=\"microservice-metrics\",service_name=~\"auth-service|user-service|product-service|voucher-service|order-service|notification-service|api-gateway\"}[5m]) * 100", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "Local All Services CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "Memory usage of local business services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 22}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_resident_memory_bytes{job=\"microservice-metrics\",service_name=~\"auth-service|user-service|product-service|voucher-service|order-service|notification-service|api-gateway\"}", "legendFormat": "{{service_name}}", "refId": "A"}], "title": "Local All Services Memory Usage", "type": "timeseries"}], "preload": false, "refresh": "30s", "schemaVersion": 41, "tags": ["infrastructure", "monitoring", "vps", "local"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Infrastructure Overview - VPS & Local Services", "uid": "infrastructure-overview", "version": 1}