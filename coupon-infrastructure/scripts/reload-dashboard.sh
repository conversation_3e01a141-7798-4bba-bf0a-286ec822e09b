#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo -e "${BLUE}🔄 Reloading Grafana Dashboard${NC}"
echo "=================================="
echo ""

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the coupon-infrastructure directory."
    exit 1
fi

# Check if <PERSON><PERSON> is running
if ! docker compose ps grafana | grep -q "Up"; then
    print_error "Grafana container is not running. Please start the infrastructure first:"
    echo "  make start"
    exit 1
fi

print_status "Restarting Grafana to reload dashboards..."

# Restart Grafana container
docker compose restart grafana

# Wait for Grafana to be healthy
print_status "Waiting for Grafana to be ready..."
sleep 10

# Check if Grafana is healthy
for i in {1..30}; do
    if docker compose ps grafana | grep -q "healthy"; then
        print_success "Grafana is ready!"
        break
    elif [ $i -eq 30 ]; then
        print_error "Grafana failed to start properly. Check logs:"
        echo "  make logs SERVICE=grafana"
        exit 1
    else
        echo -n "."
        sleep 2
    fi
done

echo ""
print_success "Dashboard reload completed!"
echo ""
print_status "Access your updated dashboard at:"
echo "  🌐 http://localhost:3000 (admin/admin123)"
echo ""
print_status "Navigate to: Dashboards → Infrastructure Overview"
