#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
INFRASTRUCTURE_DIR="$PROJECT_ROOT"

if [ -f "$PROJECT_ROOT/.env" ]; then
    source "$PROJECT_ROOT/.env"
fi

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

create_directories() {
    print_status "Creating infrastructure directories..."

    mkdir -p "$INFRASTRUCTURE_DIR/prometheus/data"
    mkdir -p "$INFRASTRUCTURE_DIR/grafana/data"
    mkdir -p "$INFRASTRUCTURE_DIR/alertmanager/data"

    sudo chown -R 472:472 "$INFRASTRUCTURE_DIR/grafana/data" 2>/dev/null || {
        print_warning "Could not set Grafana permissions. You may need to run: sudo chown -R 472:472 grafana/data"
    }

    print_success "Infrastructure directories created"
}

validate_configs() {
    print_status "Validating configuration files..."

    if [ ! -f "$INFRASTRUCTURE_DIR/prometheus/prometheus.yml" ]; then
        print_error "Prometheus configuration file not found: $INFRASTRUCTURE_DIR/prometheus/prometheus.yml"
        exit 1
    fi

    if [ ! -f "$INFRASTRUCTURE_DIR/alertmanager/alertmanager.yml" ]; then
        print_error "AlertManager configuration file not found: $INFRASTRUCTURE_DIR/alertmanager/alertmanager.yml"
        exit 1
    fi

    if [ ! -d "$INFRASTRUCTURE_DIR/grafana/provisioning" ]; then
        print_error "Grafana provisioning directory not found: $INFRASTRUCTURE_DIR/grafana/provisioning"
        exit 1
    fi

    if [ ! -f "$INFRASTRUCTURE_DIR/docker-compose.yml" ]; then
        print_error "Docker Compose file not found: $INFRASTRUCTURE_DIR/docker-compose.yml"
        exit 1
    fi 

    print_success "Configuration files validated"
}

start_infrastructure() {
    print_status "Starting infrastructure stack..."

    cd "$PROJECT_ROOT"

    # Start services
    docker compose up -d

    print_success "Infrastructure stack started"
}

wait_for_services() {
    print_status "Waiting for services to be ready..."

    print_status "Waiting for Prometheus..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec prometheus wget --no-verbose --tries=1 --spider http://localhost:9090/-/ready 2>/dev/null; then
            print_success "Prometheus is ready (internal)"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done

    if [ $timeout -le 0 ]; then
        print_error "Prometheus failed to start within timeout"
        exit 1
    fi
    
    print_status "Waiting for Grafana..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:3000/api/health >/dev/null 2>&1; then
            print_success "Grafana is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Grafana failed to start within timeout"
        exit 1
    fi
    
    print_status "Waiting for AlertManager..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9093/-/ready >/dev/null 2>&1; then
            print_success "AlertManager is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "AlertManager failed to start within timeout"
        exit 1
    fi

    print_status "Waiting for Kafka..."
    timeout=90
    while [ $timeout -gt 0 ]; do
        if docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list >/dev/null 2>&1; then
            print_success "Kafka is ready"
            break
        fi
        sleep 3
        timeout=$((timeout - 3))
    done

    if [ $timeout -le 0 ]; then
        print_warning "Kafka may not be fully ready yet (this is normal for first startup)"
    fi

    print_status "Waiting for Jaeger..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:16686/ >/dev/null 2>&1; then
            print_success "Jaeger is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done

    if [ $timeout -le 0 ]; then
        print_warning "Jaeger may not be fully ready yet"
    fi
}

display_urls() {
    print_success "Infrastructure stack deployed successfully!"
    echo ""
    echo -e "${CYAN}🌐 VPS Infrastructure Service URLs:${NC}"
    echo "=================================="
    echo ""
    echo -e "${GREEN}External Access (Public):${NC}"
    echo "  Grafana:      http://localhost:3000 (admin/admin123)"
    echo "  Pushgateway:  http://localhost:9091"
    echo "  Alertmanager: http://localhost:9093"
    echo "  Jaeger UI:    http://localhost:16686"
    echo "  Kafka UI:     http://localhost:8090"
    echo ""
    echo -e "${YELLOW}Internal Access (VPS Only):${NC}"
    echo "  Prometheus:   http://localhost:9090 (use Grafana instead)"
    echo "  Node Exporter: http://localhost:9100 (metrics via Grafana)"
    echo "  cAdvisor:     http://localhost:8080 (metrics via Grafana)"
    echo "  Kafka:        localhost:9092"
    echo ""
    echo -e "${PURPLE}📋 Management Commands:${NC}"
    echo "  View logs:    docker compose logs -f [service]"
    echo "  Stop stack:   docker compose down"
    echo "  Health check: make health"
    echo "  Service URLs: make urls"
    echo ""
}

run_health_checks() {
    print_status "Running health checks..."

    print_status "Checking Prometheus targets (internal)..."
    if docker exec prometheus wget --no-verbose --tries=1 --spider http://localhost:9090/api/v1/targets 2>/dev/null; then
        targets_response=$(docker exec prometheus wget -qO- http://localhost:9090/api/v1/targets 2>/dev/null)
        if echo "$targets_response" | grep -q '"health":"up"'; then
            print_success "Some Prometheus targets are healthy"
        else
            print_warning "No healthy Prometheus targets found. Services may not be running."
        fi
    else
        print_error "Cannot reach Prometheus for health check"
    fi

    print_status "Checking Grafana datasources..."
    if curl -s -u admin:admin123 http://localhost:3000/api/datasources | grep -q "Prometheus"; then
        print_success "Grafana Prometheus datasource configured"
    else
        print_warning "Grafana Prometheus datasource not found"
    fi

    print_status "Checking Push Gateway..."
    if curl -s http://localhost:9091/metrics >/dev/null 2>&1; then
        print_success "Push Gateway is accessible"
    else
        print_warning "Push Gateway is not accessible"
    fi

    print_status "Checking Kafka..."
    if docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list >/dev/null 2>&1; then
        topic_count=$(docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list 2>/dev/null | wc -l)
        print_success "Kafka is accessible (topics: $topic_count)"
    else
        print_warning "Kafka is not accessible"
    fi

    print_status "Checking Kafka UI..."
    if curl -s http://localhost:8090/ >/dev/null 2>&1; then
        print_success "Kafka UI is accessible"
    else
        print_warning "Kafka UI is not accessible"
    fi

    print_status "Checking Jaeger..."
    if curl -s http://localhost:16686/ >/dev/null 2>&1; then
        print_success "Jaeger UI is accessible"
    else
        print_warning "Jaeger UI is not accessible"
    fi

    print_status "Checking AlertManager..."
    if curl -s http://localhost:9093/-/ready >/dev/null 2>&1; then
        print_success "AlertManager is accessible"
    else
        print_warning "AlertManager is not accessible"
    fi
}

main() {
    echo -e "${CYAN}========================================================${NC}"
    echo -e "${CYAN}  Coupon Infrastructure - VPS Deployment Script${NC}"
    echo -e "${CYAN}========================================================${NC}"
    echo ""
    echo -e "${BLUE}🚀 Deploying infrastructure services to VPS...${NC}"
    echo ""

    create_directories
    validate_configs
    start_infrastructure
    wait_for_services
    run_health_checks
    display_urls

    echo ""
    print_success "🎉 Infrastructure deployment completed successfully!"
    echo ""
    echo -e "${CYAN}📝 Next Steps:${NC}"
    echo "1. Configure local services to connect to this VPS"
    echo "2. Update metrics pusher with VPS IP: YOUR_VPS_IP"
    echo "3. Start local business services"
    echo "4. Verify metrics are flowing in Grafana"
}

case "${1:-}" in
    "start")
        start_infrastructure
        wait_for_services
        display_urls
        ;;
    "stop")
        print_status "Stopping infrastructure stack..."
        cd "$PROJECT_ROOT"
        docker compose down
        print_success "Infrastructure stack stopped"
        ;;
    "restart")
        print_status "Restarting infrastructure stack..."
        cd "$PROJECT_ROOT"
        docker compose down
        docker compose up -d
        wait_for_services
        display_urls
        ;;
    "status")
        print_status "Checking infrastructure stack status..."
        cd "$PROJECT_ROOT"
        docker compose ps
        ;;
    "health")
        run_health_checks
        ;;
     "help")
        echo "Usage: $0 [start|stop|restart|status|logs|health|help]"
        echo ""
        echo "Coupon Infrastructure - VPS Deployment Script"
        echo "=============================================="
        echo ""
        echo "This script manages the infrastructure stack deployment including:"
        echo "- Prometheus (metrics collection) - Internal only"
        echo "- Grafana (visualization dashboards) - External access"
        echo "- AlertManager (alerting) - External access"
        echo "- Node Exporter (system metrics) - Internal only"
        echo "- cAdvisor (container metrics) - Internal only"
        echo "- Push Gateway (metrics collection) - External access"
        echo "- Kafka (message broker) - External access"
        echo "- Kafka UI (Kafka management) - External access"
        echo "- Jaeger (distributed tracing) - External access"
        echo ""
        echo "Commands:"
        echo "  (no args)  Deploy complete infrastructure stack (default)"
        echo "  start      Start infrastructure services"
        echo "  stop       Stop infrastructure services"
        echo "  restart    Restart infrastructure services"
        echo "  status     Show infrastructure services status"
        echo "  logs       Show infrastructure services logs"
        echo "             Use: $0 logs [service-name] for specific service"
        echo "  health     Run health checks on infrastructure services"
        echo "  help       Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Deploy complete stack"
        echo "  $0 start              # Start services"
        echo "  $0 logs prometheus    # Show Prometheus logs"
        echo "  $0 health             # Check service health"
        echo ""
        echo "Service URLs (after deployment):"
        echo "  Grafana:      http://localhost:3000 (admin/admin123)"
        echo "  Push Gateway: http://localhost:9091"
        echo "  AlertManager: http://localhost:9093"
        echo "  Jaeger UI:    http://localhost:16686"
        echo "  Kafka UI:     http://localhost:8090"
        echo ""
        echo "Internal Only (use Grafana for access):"
        echo "  Prometheus:   Internal only (use Grafana)"
        echo "  Node Exporter: Internal only (metrics via Grafana)"
        echo "  cAdvisor:     Internal only (metrics via Grafana)"
        echo "  Kafka:        localhost:9092"
        echo ""
        echo "Security Note:"
        echo "  This deployment uses a secure configuration with internal-only"
        echo "  access for Prometheus, Node Exporter, and cAdvisor."
        echo "  External services (Grafana, Jaeger, Kafka UI) are accessible via web UI."
        echo ""
        echo "Prerequisites:"
        echo "  - Docker and Docker Compose installed"
        echo "  - Docker daemon running"
        ;;
    *)
        main
        ;;
esac
