#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo -e "${BLUE}🔍 Checking Available Metrics${NC}"
echo "=================================="
echo ""

# Check if Prometheus is accessible
PROMETHEUS_URL="http://localhost:9090"

print_status "Checking Prometheus connectivity..."
if ! curl -s "$PROMETHEUS_URL/api/v1/query?query=up" > /dev/null; then
    echo -e "${RED}❌ Cannot connect to Prometheus at $PROMETHEUS_URL${NC}"
    echo "Make sure Prometheus is running and accessible."
    exit 1
fi

print_success "Prometheus is accessible"
echo ""

print_status "Checking container metrics for jaeger, kafka, kafka-ui..."
echo ""

# Check container_last_seen metrics
echo "=== container_last_seen metrics ==="
curl -s "$PROMETHEUS_URL/api/v1/query?query=container_last_seen{name=~\"jaeger|kafka|kafka-ui\"}" | jq -r '.data.result[] | "\(.metric.name): \(.value[1])"' 2>/dev/null || echo "No container_last_seen metrics found"

echo ""
echo "=== up metrics for containers ==="
curl -s "$PROMETHEUS_URL/api/v1/query?query=up{container_name=~\"jaeger|kafka|kafka-ui\"}" | jq -r '.data.result[] | "\(.metric.container_name): \(.value[1])"' 2>/dev/null || echo "No up metrics with container_name found"

echo ""
echo "=== All up metrics ==="
curl -s "$PROMETHEUS_URL/api/v1/query?query=up" | jq -r '.data.result[] | "\(.metric.job // .metric.instance // .metric.name // "unknown"): \(.value[1])"' 2>/dev/null | grep -E "(jaeger|kafka)" || echo "No jaeger/kafka in up metrics"

echo ""
echo "=== Container CPU metrics ==="
curl -s "$PROMETHEUS_URL/api/v1/query?query=container_cpu_usage_seconds_total{name=~\"jaeger|kafka|kafka-ui\"}" | jq -r '.data.result[] | "\(.metric.name): \(.value[1])"' 2>/dev/null | head -3 || echo "No container CPU metrics found"

echo ""
echo "=== Container Memory metrics ==="
curl -s "$PROMETHEUS_URL/api/v1/query?query=container_memory_usage_bytes{name=~\"jaeger|kafka|kafka-ui\"}" | jq -r '.data.result[] | "\(.metric.name): \(.value[1])"' 2>/dev/null | head -3 || echo "No container memory metrics found"

echo ""
print_status "Check complete!"
