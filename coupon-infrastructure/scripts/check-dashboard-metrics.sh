#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 Dashboard Metrics Diagnostic${NC}"
echo "=================================================="

# Check if infrastructure services are running
echo -e "${BLUE}📊 Checking Infrastructure Services...${NC}"
echo ""

# Check Docker containers
echo -e "${YELLOW}Docker Containers:${NC}"
if command -v docker &> /dev/null; then
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(prometheus|grafana|cadvisor|node-exporter|pushgateway|alertmanager|jaeger|kafka|kafka-ui)" || echo "No infrastructure containers running"
else
    echo "Docker not available"
fi

echo ""

# Check if Prometheus is accessible
echo -e "${YELLOW}Prometheus Accessibility:${NC}"
if curl -s http://localhost:9090/-/healthy > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Prometheus is accessible at http://localhost:9090${NC}"
    
    # Check targets
    echo -e "${YELLOW}Prometheus Targets:${NC}"
    curl -s http://localhost:9090/api/v1/targets | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    for target in data['data']['activeTargets']:
        job = target['labels']['job']
        health = target['health']
        endpoint = target['scrapeUrl']
        status = '✅' if health == 'up' else '❌'
        print(f'{status} {job}: {health} ({endpoint})')
except Exception as e:
    print(f'Error parsing targets: {e}')
"
else
    echo -e "${RED}❌ Prometheus is not accessible at http://localhost:9090${NC}"
fi

echo ""

# Check specific metrics
echo -e "${YELLOW}Checking Key Metrics:${NC}"

# VPS Infrastructure Services metrics
echo -e "${BLUE}VPS Infrastructure Services:${NC}"
if curl -s http://localhost:9090/api/v1/query?query=up 2>/dev/null | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data['status'] == 'success':
        for result in data['data']['result']:
            job = result['metric'].get('job', 'unknown')
            value = result['value'][1]
            status = '✅' if value == '1' else '❌'
            print(f'{status} {job}: {value}')
    else:
        print('Query failed')
except Exception as e:
    print(f'Error: {e}')
" 2>/dev/null; then
    :
else
    echo "Cannot query Prometheus metrics"
fi

echo ""

# Check container metrics
echo -e "${BLUE}Container Metrics:${NC}"
if curl -s "http://localhost:9090/api/v1/query?query=container_last_seen" 2>/dev/null | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data['status'] == 'success':
        for result in data['data']['result']:
            name = result['metric'].get('name', 'unknown')
            value = result['value'][1]
            print(f'📦 {name}: last seen {value}s ago')
    else:
        print('No container metrics found')
except Exception as e:
    print(f'Error: {e}')
" 2>/dev/null; then
    :
else
    echo "Cannot query container metrics"
fi

echo ""

# Check microservice metrics
echo -e "${BLUE}Microservice Metrics:${NC}"
if curl -s "http://localhost:9090/api/v1/query?query=up{job=\"microservice-metrics\"}" 2>/dev/null | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if data['status'] == 'success' and data['data']['result']:
        for result in data['data']['result']:
            service = result['metric'].get('service_name', 'unknown')
            value = result['value'][1]
            status = '✅' if value == '1' else '❌'
            print(f'{status} {service}: {value}')
    else:
        print('No microservice metrics found')
except Exception as e:
    print(f'Error: {e}')
" 2>/dev/null; then
    :
else
    echo "Cannot query microservice metrics"
fi

echo ""

# Check Pushgateway
echo -e "${YELLOW}Pushgateway Status:${NC}"
if curl -s http://localhost:9091/metrics > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Pushgateway is accessible at http://localhost:9091${NC}"
    
    # Count metrics
    metric_count=$(curl -s http://localhost:9091/metrics | grep -c "^[a-zA-Z]" || echo "0")
    echo "📊 Total metrics in Pushgateway: $metric_count"
    
    # Check for service metrics
    service_metrics=$(curl -s http://localhost:9091/metrics | grep -c "service_name" || echo "0")
    echo "🔧 Service-specific metrics: $service_metrics"
else
    echo -e "${RED}❌ Pushgateway is not accessible at http://localhost:9091${NC}"
fi

echo ""

# Recommendations
echo -e "${BLUE}📋 Recommendations:${NC}"
echo "=================================================="

if ! curl -s http://localhost:9090/-/healthy > /dev/null 2>&1; then
    echo -e "${YELLOW}1. Start infrastructure services:${NC}"
    echo "   cd coupon-infrastructure && make deploy"
fi

if ! curl -s "http://localhost:9090/api/v1/query?query=up{job=\"microservice-metrics\"}" 2>/dev/null | grep -q "service_name"; then
    echo -e "${YELLOW}2. Start business services:${NC}"
    echo "   ./scripts/start-local-services.sh"
fi

echo -e "${YELLOW}3. Check Grafana dashboard:${NC}"
echo "   http://localhost:3000/d/infrastructure-overview"

echo ""
echo -e "${GREEN}🎉 Diagnostic complete!${NC}"
