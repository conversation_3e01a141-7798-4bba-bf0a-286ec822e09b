#!/bin/bash

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

BENCHMARK_DURATION=${BENCHMARK_DURATION:-30s}
BENCHMARK_TIMEOUT=${BENCHMARK_TIMEOUT:-30m}
PUSH_GATEWAY_URL=${PUSH_GATEWAY_URL:-http://localhost:9091}
RESULTS_DIR="./benchmark-results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

SERVICES=(
    "coupon-auth-service"
    "coupon-user-service"
    "coupon-product-service"
    "coupon-voucher-service"
    "coupon-order-service"
    "coupon-notification-service"
    "coupon-api-gateway"
)

get_services_to_run() {
    local services_to_run=()

    if [ "${AUTH_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-auth-service")
    fi
    if [ "${USER_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-user-service")
    fi
    if [ "${VOUCHER_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-voucher-service")
    fi
    if [ "${PRODUCT_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-product-service")
    fi
    if [ "${ORDER_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-order-service")
    fi
    if [ "${NOTIFICATION_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-notification-service")
    fi
    if [ "${API_GATEWAY_ONLY:-false}" = "true" ]; then
        services_to_run+=("coupon-api-gateway")
    fi

    if [ ${#services_to_run[@]} -eq 0 ]; then
        services_to_run=("${SERVICES[@]}")
    fi

    echo "${services_to_run[@]}"
}

echo -e "${BLUE}=== Comprehensive Microservices Benchmark Runner ===${NC}"
echo -e "${BLUE}Timestamp: ${TIMESTAMP}${NC}"
echo -e "${BLUE}Duration: ${BENCHMARK_DURATION}${NC}"
echo -e "${BLUE}Push Gateway: ${PUSH_GATEWAY_URL}${NC}"
echo

check_service_exists() {
    local service=$1
    if [ ! -d "../$service" ]; then
        echo -e "${YELLOW}⚠ Service directory not found: $service${NC}"
        return 1
    fi
    return 0
}

check_benchmark_exists() {
    local service=$1
    if [ ! -d "../$service/benchmark" ]; then
        echo -e "${YELLOW}⚠ No benchmark directory found for: $service${NC}"
        return 1
    fi
    return 0
}

start_monitoring() {
    echo -e "${BLUE}Starting monitoring stack...${NC}"
    
    if docker ps | grep -q "pushgateway"; then
        echo -e "${GREEN}✓ Monitoring stack already running${NC}"
    else
        echo "Starting Prometheus, Grafana, and Push Gateway..."
        docker compose up -d
        
        echo "Waiting for services to be ready..."
        sleep 30
        
        if docker exec prometheus wget --no-verbose --tries=1 --spider http://localhost:9090/-/healthy 2>/dev/null; then
            echo -e "${GREEN}✓ Prometheus is healthy (internal)${NC}"
        else
            echo -e "${RED}✗ Prometheus health check failed${NC}"
        fi
        
        if curl -s http://localhost:9091/metrics > /dev/null; then
            echo -e "${GREEN}✓ Push Gateway is healthy${NC}"
        else
            echo -e "${RED}✗ Push Gateway health check failed${NC}"
        fi
        
        if curl -s http://localhost:3000/api/health > /dev/null; then
            echo -e "${GREEN}✓ Grafana is healthy${NC}"
        else
            echo -e "${RED}✗ Grafana health check failed${NC}"
        fi
    fi
}

run_service_benchmark() {
    local service=$1
    echo -e "${BLUE}Running benchmarks for: $service${NC}"
    
    cd "../$service"
    
    mkdir -p benchmark-results
    
    if [ -f "docker-compose.yml" ]; then
        echo "Starting service dependencies..."
        docker compose up -d postgres-* redis-* 2>/dev/null || true
        sleep 10
    fi
    
    echo "Executing benchmark tests..."
    local result_file="benchmark-results/${service}_benchmark_${TIMESTAMP}.txt"
    
    if go test -bench=. -benchmem -timeout=${BENCHMARK_TIMEOUT} \
        -benchtime=${BENCHMARK_DURATION} \
        ./benchmark/... > "$result_file" 2>&1; then
        echo -e "${GREEN}✓ Benchmarks completed successfully${NC}"
        
        local ops_per_sec=$(grep -E "BenchmarkAuth.*-.*ops" "$result_file" | tail -1 | awk '{print $3}' || echo "N/A")
        local mem_allocs=$(grep -E "BenchmarkAuth.*allocs/op" "$result_file" | tail -1 | awk '{print $5}' || echo "N/A")
        
        echo -e "  ${GREEN}Performance Summary:${NC}"
        echo -e "    Operations/sec: $ops_per_sec"
        echo -e "    Memory allocs/op: $mem_allocs"
        echo -e "    Results saved: $result_file"
        
    else
        echo -e "${RED}✗ Benchmarks failed${NC}"
        echo -e "  Check results file: $result_file"
    fi
    
    cd - > /dev/null
}

generate_summary_report() {
    echo -e "${BLUE}Generating benchmark summary report...${NC}"
    
    local summary_file="$RESULTS_DIR/benchmark_summary_${TIMESTAMP}.md"
    mkdir -p "$RESULTS_DIR"
    
    cat > "$summary_file" << EOF
# Benchmark Summary Report

**Timestamp:** $(date)
**Duration:** $BENCHMARK_DURATION
**Services Tested:** ${#SERVICES[@]}

## Results Overview

| Service | Status | Results File |
|---------|--------|--------------|
EOF

    for service in "${SERVICES[@]}"; do
        local result_file="../$service/benchmark-results/${service}_benchmark_${TIMESTAMP}.txt"
        if [ -f "$result_file" ]; then
            echo "| $service | ✅ Success | $result_file |" >> "$summary_file"
        else
            echo "| $service | ❌ Failed/Skipped | N/A |" >> "$summary_file"
        fi
    done
    
    cat >> "$summary_file" << EOF

## Monitoring Links

- **Prometheus:** http://localhost:9090
- **Grafana:** http://localhost:3000 (admin/admin123)
- **Push Gateway:** http://localhost:9091
- **Benchmark Dashboard:** http://localhost:3000/d/benchmark-metrics

## Next Steps

1. Review individual benchmark results in each service directory
2. Check Grafana dashboard for performance trends
3. Investigate any failed benchmarks
4. Compare results with previous runs for regression detection

---
*Generated by automated benchmark runner*
EOF

    echo -e "${GREEN}✓ Summary report generated: $summary_file${NC}"
}

open_dashboards() {
    echo -e "${BLUE}Opening monitoring dashboards...${NC}"
    
    if command -v open > /dev/null; then
        echo "Opening Grafana dashboard..."
        open "http://localhost:3000/d/benchmark-metrics"
        sleep 2
        echo "Opening Prometheus..."
        open "http://localhost:9090"
    else
        echo -e "${YELLOW}Manual dashboard access:${NC}"
        echo "  Grafana: http://localhost:3000/d/benchmark-metrics"
        echo "  Prometheus: http://localhost:9090"
        echo "  Push Gateway: http://localhost:9091"
    fi
}

main() {
    echo -e "${BLUE}Starting comprehensive benchmark run...${NC}"
    
    start_monitoring
    
    local successful_services=0
    local failed_services=0
    local skipped_services=0
    
    local services_to_run=($(get_services_to_run))

    echo -e "${BLUE}Services to benchmark: ${services_to_run[*]}${NC}"
    echo

    for service in "${services_to_run[@]}"; do
        echo
        echo -e "${BLUE}=== Processing: $service ===${NC}"
        
        if ! check_service_exists "$service"; then
            ((skipped_services++))
            continue
        fi
        
        if ! check_benchmark_exists "$service"; then
            ((skipped_services++))
            continue
        fi
        
        if run_service_benchmark "$service"; then
            ((successful_services++))
        else
            ((failed_services++))
        fi
        
        sleep 5
    done
    
    echo
    echo -e "${BLUE}=== Benchmark Run Complete ===${NC}"
    echo -e "${GREEN}✓ Successful: $successful_services${NC}"
    echo -e "${RED}✗ Failed: $failed_services${NC}"
    echo -e "${YELLOW}⚠ Skipped: $skipped_services${NC}"
    
    generate_summary_report
    
    if [ "$1" != "--no-open" ]; then
        open_dashboards
    fi
    
    echo
    echo -e "${GREEN}🎉 Benchmark run completed successfully!${NC}"
    echo -e "${BLUE}Check the Grafana dashboard for detailed metrics and trends.${NC}"
}

case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --no-open      Don't automatically open dashboards"
        echo "  --duration=X   Set benchmark duration (default: 30s)"
        echo "  --services=X   Comma-separated list of services to benchmark"
        echo
        echo "Environment variables:"
        echo "  BENCHMARK_DURATION  Benchmark duration (default: 30s)"
        echo "  BENCHMARK_TIMEOUT   Benchmark timeout (default: 30m)"
        echo "  PUSH_GATEWAY_URL    Push Gateway URL (default: http://localhost:9091)"
        exit 0
        ;;
    --duration=*)
        BENCHMARK_DURATION="${1#*=}"
        shift
        ;;
    --services=*)
        IFS=',' read -ra SERVICES <<< "${1#*=}"
        shift
        ;;
esac

main "$@"

