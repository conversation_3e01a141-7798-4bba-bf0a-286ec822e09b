global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: "coupon-microservices"
    environment: "development"

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "node-exporter"
    static_configs:
      - targets: ["node-exporter:9100"]
    relabel_configs:
      - source_labels: [__address__]
        target_label: location
        replacement: "vps"
      - source_labels: [__address__]
        target_label: component_type
        replacement: "infrastructure"

  - job_name: "cadvisor"
    static_configs:
      - targets: ["cadvisor:8080"]
    relabel_configs:
      - source_labels: [__address__]
        target_label: location
        replacement: "vps"
      - source_labels: [__address__]
        target_label: component_type
        replacement: "infrastructure"

  - job_name: "pushgateway"
    static_configs:
      - targets: ["pushgateway:9091"]
    metrics_path: "/metrics"
    scrape_interval: 5s
    scrape_timeout: 5s
    honor_labels: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: "pushgateway"

  - job_name: "benchmark-metrics"
    static_configs:
      - targets: ["pushgateway:9091"]
    metrics_path: "/metrics"
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: "benchmark_.*"
        target_label: __tmp_benchmark_metric
        replacement: "true"
      - source_labels: [__tmp_benchmark_metric]
        regex: "true"
        target_label: metric_type
        replacement: "benchmark"

  - job_name: "microservice-metrics"
    static_configs:
      - targets: ["pushgateway:9091"]
    metrics_path: "/metrics"
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: "benchmark_.*"
        action: drop
      - source_labels: [service_name]
        regex: ".*"
        target_label: location
        replacement: "local"
      - source_labels: [service_name]
        regex: ".*"
        target_label: component_type
        replacement: "business_service"
      - source_labels: [__name__]
        regex: ".*"
        target_label: metric_type
        replacement: "microservice"
