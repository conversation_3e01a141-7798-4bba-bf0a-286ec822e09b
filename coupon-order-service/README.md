# Coupon Order Service

A comprehensive order management service for the coupon microservice system, built with Go and gRPC. This service handles order creation, voucher integration, discount calculations, and order lifecycle management.

## 🎯 Service Overview

The Coupon Order Service provides:

- **Order Management**: Complete order lifecycle from creation to completion
- **Voucher Integration**: Seamless voucher validation and discount application
- **Discount Calculations**: Automatic discount computation with voucher service
- **Order Tracking**: Real-time order status updates and history
- **User Authentication**: JWT-based user authentication from cookies
- **Health Monitoring**: Comprehensive health checks and metrics

### Key Features

- **Order CRUD Operations**: Complete order lifecycle management
- **Voucher Validation**: Real-time voucher eligibility checking
- **Discount Application**: Automatic discount calculation and application
- **Order Status Management**: Track orders through various states
- **User Context**: Extract user information from JWT cookies
- **Database Integration**: PostgreSQL with GORM for data persistence and auto-migration
- **gRPC Authentication Middleware**: Service-to-service authentication
- **Distributed Tracing**: <PERSON><PERSON>ger integration for request tracing
- **Metrics Collection**: Prometheus metrics for monitoring and alerting
- **Health Checks**: Service health monitoring with dependency checks

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│   Order Service  │───▶│   PostgreSQL    │
│                 │    │                  │    │                 │
│ - Order APIs    │    │ - Order CRUD     │    │ - Orders        │
│ - JWT Cookies   │    │ - Voucher Check  │    │ - Order Items   │
│ - User Context  │    │ - Discount Calc  │    │ - Order History │
└─────────────────┘    │ - Status Mgmt    │    └─────────────────┘
                       │ - Health Checks  │
┌─────────────────┐    └──────────────────┘
│ Voucher Service │◀───────────────────────────┐
│                 │    Voucher Validation      │
│ - Eligibility   │    & Discount Calculation  │
│ - Usage Track   │                            │
└─────────────────┘                            │
                                               │
┌─────────────────┐                            │
│  User Service   │◀───────────────────────────┘
│                 │    User Information
│ - User Details  │    Retrieval
│ - User Types    │
└─────────────────┘
```

## 📋 gRPC API Methods

### Order Management
- `CreateOrder(CreateOrderRequest) → CreateOrderResponse` - Create new order with voucher validation
- `GetOrder(GetOrderRequest) → GetOrderResponse` - Get order by ID
- `ListOrders(ListOrdersRequest) → ListOrdersResponse` - List user's orders with pagination
- `UpdateOrderStatus(UpdateOrderStatusRequest) → UpdateOrderStatusResponse` - Update order status

### Health Check
- `HealthCheck(HealthCheckRequest) → HealthCheckResponse` - Service health status

## 🔧 Configuration

### Environment Variables

```bash
# Service Configuration
ORDER_SERVICE_CLIENT_ID=order-service-client-id
ORDER_SERVICE_CLIENT_KEY=order-service-client-secret

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=coupon_order
POSTGRES_SSLMODE=disable

# Auth Service Configuration
AUTH_SERVICE_ADDR=auth-service:50051

# Voucher Service Configuration
VOUCHER_SERVICE_ADDR=voucher-service:50054

# User Service Configuration
USER_SERVICE_ADDR=user-service:50051

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key

# Observability
JAEGER_HOST=jaeger
JAEGER_PORT=6831
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Metrics
METRICS_ENABLED=true
METRICS_PUSHGATEWAY_URL=http://pushgateway:9091
METRICS_PUSH_INTERVAL=30s
METRICS_INSTANCE_ID=order-service-1
```

## 🗄️ Database Schema

### Orders Table
```sql
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    final_amount DECIMAL(10,2) NOT NULL,
    voucher_id INTEGER,
    voucher_code VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Order Items Table
```sql
CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    product_id INTEGER NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Order Status Values
- `PENDING` - Order created, awaiting processing
- `CONFIRMED` - Order confirmed and being prepared
- `PROCESSING` - Order is being processed
- `SHIPPED` - Order has been shipped
- `DELIVERED` - Order successfully delivered
- `CANCELLED` - Order cancelled
- `REFUNDED` - Order refunded
## 🚀 Getting Started

### Prerequisites
- Go >= 1.24
- PostgreSQL 13+
- Docker and Docker Compose

### Local Development
1. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Build and run**
   ```bash
   make build
   make run
   ```

3. **Access the service**
   - gRPC Server: `localhost:50056`
   - HTTP Health Check: `http://localhost:8085/health`
   - Metrics: `http://localhost:8085/metrics`

### Docker Deployment
```bash
# Start with all dependencies
make compose-up

# Stop services
make compose-down
```

## 🛒 Order Flow

### Order Creation Process
1. **User Authentication**: Extract user ID from JWT cookie
2. **Product Validation**: Validate product IDs and calculate totals
3. **Voucher Validation**: Check voucher eligibility (if provided)
4. **Discount Calculation**: Apply voucher discount if eligible
5. **Order Creation**: Create order and order items in database
6. **Voucher Usage**: Mark voucher as used (if applicable)
7. **Response**: Return order details with final amounts

### Order Status Lifecycle
```
PENDING → CONFIRMED → PROCESSING → SHIPPED → DELIVERED
    ↓         ↓           ↓          ↓
CANCELLED ← CANCELLED ← CANCELLED ← CANCELLED
    ↓
REFUNDED
```

## 🧪 Testing

### Unit Tests
```bash
make test
```

### gRPC Testing
```bash
# Create order without voucher
grpcurl -plaintext -d '{"items":[{"product_id":1,"quantity":2,"unit_price":25.00}],"total_amount":50.00}' \
  localhost:50056 order.OrderService/CreateOrder

# Create order with voucher
grpcurl -plaintext -d '{"items":[{"product_id":1,"quantity":2,"unit_price":25.00}],"total_amount":50.00,"voucher_code":"SAVE10"}' \
  localhost:50056 order.OrderService/CreateOrder

# Get order by ID
grpcurl -plaintext -d '{"order_id":1}' \
  localhost:50056 order.OrderService/GetOrder

# List user orders
grpcurl -plaintext -d '{"user_id":123,"limit":10,"offset":0}' \
  localhost:50056 order.OrderService/ListOrders

# Update order status
grpcurl -plaintext -d '{"order_id":1,"status":"CONFIRMED"}' \
  localhost:50056 order.OrderService/UpdateOrderStatus
```

## 📊 Monitoring

### Health Checks
```bash
curl http://localhost:8085/health
```

### Metrics
- Order creation rates and success/failure ratios
- Voucher usage rates in orders
- Discount amount distributions
- Order status transition metrics
- Database connection pool stats
- gRPC request metrics and response times

## 🔧 Development

### Project Structure
```
coupon-order-service/
├── cmd/server/main.go              # Application entry point
├── internal/
│   ├── handler/grpc/               # gRPC handlers
│   │   └── order_handler.go
│   ├── service/order_service.go    # Core business logic
│   ├── repository/order_repo.go    # Data access layer
│   ├── model/                      # Data models
│   │   ├── order.go
│   │   └── order_item.go
│   ├── clients/                    # gRPC clients
│   │   ├── voucher_client.go
│   │   └── user_client.go
│   └── middleware/grpc_auth.go     # Authentication middleware
├── config/config.yaml              # Service configuration
├── Dockerfile                      # Container image
├── docker-compose.yml              # Local development
└── Makefile                        # Build commands
```

### Business Logic Flow
1. **Order Validation**: Validate order items and calculate totals
2. **User Context**: Extract user information from JWT token
3. **Voucher Processing**: Validate and apply voucher discounts
4. **Order Persistence**: Save order and items to database
5. **Status Management**: Track order through lifecycle states

## 🚨 Error Handling

### gRPC Error Codes
- `OK` - Successful operation
- `INVALID_ARGUMENT` - Invalid request parameters or order data
- `NOT_FOUND` - Order not found
- `UNAUTHENTICATED` - Invalid or missing JWT token
- `PERMISSION_DENIED` - User not authorized to access order
- `FAILED_PRECONDITION` - Voucher invalid or order cannot be processed
- `INTERNAL` - Internal server error

## 🔒 Security Features

- **JWT Authentication**: Extract user context from HTTP-only cookies
- **Service Authentication**: gRPC middleware with client credentials
- **Input Validation**: Comprehensive order data validation
- **Authorization**: Users can only access their own orders
- **Audit Logging**: Order creation and modification logging

## 📚 Additional Resources

- [gRPC Go Documentation](https://grpc.io/docs/languages/go/)
- [GORM Documentation](https://gorm.io/docs/)
- [JWT Go Library](https://github.com/golang-jwt/jwt)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
