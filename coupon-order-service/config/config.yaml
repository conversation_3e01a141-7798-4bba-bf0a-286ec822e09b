service:
  name: "order-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${ORDER_SERVICE_CLIENT_ID}"
  client_key: "${ORDER_SERVICE_CLIENT_KEY}"

database:
  host: "postgres-order"
  port: 5432
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  name: "${POSTGRES_DB}"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  max_lifetime: "1h"

redis:
  host: "redis-order"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

downstream_services:
  auth_service_addr: "auth-service:50051"
  voucher_service_addr: "voucher-service:50051"

kafka:
  brokers: ["${KAFKA_BROKER}"]
  group_id: "order-service"
  topics:
    order_events: "order-events"
  auto_create_topics: true

jaeger:
  host: "${JAEGER_HOST}"
  port: ${JAEGER_PORT}
  endpoint: "${JAEGER_ENDPOINT}"

metrics:
  enabled: ${METRICS_ENABLED}
  pushgateway_url: "${METRICS_PUSHGATEWAY_URL}"
  push_interval: "${METRICS_PUSH_INTERVAL}"
  instance_id: "${METRICS_INSTANCE_ID}"

logging:
  level: "debug"
  format: "json"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"
