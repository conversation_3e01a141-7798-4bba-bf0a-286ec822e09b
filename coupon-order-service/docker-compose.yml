services:
  postgres-order:
    image: postgres:16-alpine
    container_name: postgres-order
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-order_db}
    ports:
      - "5437:5432"
    volumes:
      - postgres-order-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-order_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-order:
    image: redis:7-alpine
    container_name: redis-order
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6384:6379"
    volumes:
      - redis-order-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  order-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: order-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-order-service
    depends_on:
      postgres-order:
        condition: service_healthy
      redis-order:
        condition: service_healthy
    env_file:
      - .env
    ports:
      - "8085:8080"
      - "50056:50051"
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - coupon-network

volumes:
  postgres-order-data:
  redis-order-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
