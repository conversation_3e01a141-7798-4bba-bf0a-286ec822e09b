# Coupon Auth Service

A comprehensive authentication and service registry service for the coupon microservice system, built with Go and gRPC. This service manages user authentication, service-to-service authentication, and acts as a centralized service registry for all microservices.

## 🎯 Service Overview

The Coupon Auth Service provides:

- **User Authentication**: JWT token generation and validation for user sessions
- **Service Registry**: Centralized registration and validation of service credentials
- **Service-to-Service Authentication**: Client-ID/Client-Key validation for microservice communication
- **Bootstrap Token Authentication**: Secure service registration using bootstrap tokens
- **Password Security**: bcrypt hashing for secure password storage
- **Health Monitoring**: Comprehensive health checks and metrics

### Key Features

- **JWT Token Management**: Secure token generation and validation with configurable expiration
- **Service Credential Management**: Registration and validation of service client credentials
- **Bootstrap Token System**: Secure service registration with time-limited bootstrap tokens
- **Database Integration**: PostgreSQL with GORM for data persistence and auto-migration
- **gRPC Authentication Middleware**: Method-level access control for service endpoints
- **Distributed Tracing**: Jaeger integration for request tracing
- **Metrics Collection**: Prometheus metrics for monitoring and alerting
- **Health Checks**: Service health monitoring with dependency checks

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│   Auth Service   │───▶│   PostgreSQL    │
│                 │    │                  │    │                 │
│ - JWT Validation│    │ - User Auth      │    │ - Users Table   │
│ - Cookie Auth   │    │ - Service Auth   │    │ - Service Creds │
└─────────────────┘    │ - Service Registry│    │ - Bootstrap     │
                       │ - Bootstrap Auth │    │   Tokens        │
┌─────────────────┐    │ - Password Hash  │    └─────────────────┘
│ Other Services  │───▶│ - JWT Generation │
│                 │    │ - Health Checks  │
│ - User Service  │    └──────────────────┘
│ - Product Svc   │
│ - Voucher Svc   │
│ - Order Service │
│ - Notification  │
└─────────────────┘
```

## 📋 gRPC API Methods

### User Authentication

- `Login(LoginRequest) → LoginResponse` - Authenticate user with email/password
- `ValidateToken(ValidateTokenRequest) → ValidateTokenResponse` - Validate JWT token
- `GetUserFromToken(GetUserFromTokenRequest) → GetUserFromTokenResponse` - Extract user info from JWT

### Service Registry & Authentication

- `RegisterService(RegisterServiceRequest) → RegisterServiceResponse` - Register new service with bootstrap token
- `ValidateServiceCredentials(ValidateServiceCredentialsRequest) → ValidateServiceCredentialsResponse` - Validate service client-id/client-key
- `GetServiceInfo(GetServiceInfoRequest) → GetServiceInfoResponse` - Get service information by client-id

### Health Check

- `HealthCheck(HealthCheckRequest) → HealthCheckResponse` - Service health status

## 🔧 Configuration

### Environment Variables

```bash
# Service Configuration
AUTH_SERVICE_CLIENT_ID=auth-service-client-id
AUTH_SERVICE_CLIENT_KEY=auth-service-client-secret

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=coupon_auth
POSTGRES_SSLMODE=disable

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRATION_HOURS=24

# Bootstrap Token Configuration
BOOTSTRAP_TOKEN=your-bootstrap-token-for-service-registration
BOOTSTRAP_TOKEN_EXPIRY=2024-12-31T23:59:59Z

# Observability
JAEGER_HOST=jaeger
JAEGER_PORT=6831
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Metrics
METRICS_ENABLED=true
METRICS_PUSHGATEWAY_URL=http://pushgateway:9091
METRICS_PUSH_INTERVAL=30s
METRICS_INSTANCE_ID=auth-service-1
```

### Configuration File (`config/config.yaml`)

```yaml
service:
  name: "auth-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${AUTH_SERVICE_CLIENT_ID}"
  client_key: "${AUTH_SERVICE_CLIENT_KEY}"

database:
  host: "${POSTGRES_HOST}"
  port: ${POSTGRES_PORT}
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  dbname: "${POSTGRES_DB}"
  sslmode: "${POSTGRES_SSLMODE}"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

jwt:
  secret: "${JWT_SECRET}"
  expiration_hours: ${JWT_EXPIRATION_HOURS}

bootstrap:
  token: "${BOOTSTRAP_TOKEN}"
  expiry: "${BOOTSTRAP_TOKEN_EXPIRY}"

jaeger:
  host: "${JAEGER_HOST}"
  port: ${JAEGER_PORT}
  endpoint: "${JAEGER_ENDPOINT}"

metrics:
  enabled: ${METRICS_ENABLED}
  pushgateway_url: "${METRICS_PUSHGATEWAY_URL}"
  push_interval: "${METRICS_PUSH_INTERVAL}"
  instance_id: "${METRICS_INSTANCE_ID}"

logging:
  level: "debug"
  format: "json"

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"
```

## 🚀 Getting Started

### Prerequisites

- Go 1.24+
- PostgreSQL 13+
- Docker and Docker Compose

### Local Development

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd coupon-auth-service
   ```

2. **Install dependencies**

   ```bash
   go mod download
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Build and run the service**

   ```bash
   # Build the binary
   make build

   # Run the service locally
   make run
   ```

5. **Access the service**
   - gRPC Server: `localhost:50051`
   - HTTP Health Check: `http://localhost:8080/health`
   - Metrics: `http://localhost:8080/metrics`

### Docker Deployment

1. **Build the Docker image**

   ```bash
   make docker-build
   ```

2. **Run with Docker Compose**

   ```bash
   # Start the service with PostgreSQL and Jaeger
   make compose-up
   ```

   This exposes:

   - gRPC service on port `50051`
   - HTTP service on port `8080`
   - Jaeger UI on port `16686`

3. **Stop the services**
   ```bash
   make compose-down
   ```

## 🔐 Authentication & Service Registry

### User Authentication Flow

1. **User Login**: Client sends email/password to `Login` method
2. **Credential Validation**: Service validates against users table
3. **JWT Generation**: Service generates JWT token with user claims
4. **Token Response**: JWT token returned to client
5. **Token Validation**: Subsequent requests validated via `ValidateToken`

### Service Registration Flow

1. **Bootstrap Authentication**: Service uses bootstrap token for initial registration
2. **Service Registration**: Service calls `RegisterService` with service details
3. **Credential Generation**: Unique client-id and client-key generated
4. **Credential Storage**: Service credentials stored in database
5. **Service Authentication**: Service uses client-id/client-key for API calls

### CI/CD Service Registration

The service provides a CLI tool for automated service registration in CI/CD pipelines:

```bash
# Register a new service
make register-service SERVICE_NAME=user-service SERVICE_VERSION=1.0.0 DESCRIPTION="User management service"
```

**Example Output:**

```
✅ Service registration successful!
Service ID: 123e4567-e89b-12d3-a456-426614174000
Client ID: 456e7890-e89b-12d3-a456-426614174001
Client Key: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6

# Environment variables for CI/CD:
export USER_SERVICE_CLIENT_ID=456e7890-e89b-12d3-a456-426614174001
export USER_SERVICE_CLIENT_KEY=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6

📄 Credentials saved to: user-service-credentials.env
```

### Existing Service Credentials

The following services are already registered:

| Service              | Client ID                        | Purpose                |
| -------------------- | -------------------------------- | ---------------------- |
| api-gateway          | `api-gateway-client-id`          | HTTP API Gateway       |
| auth-service         | `auth-service-client-id`         | Authentication Service |
| user-service         | `user-service-client-id`         | User Management        |
| product-service      | `product-service-client-id`      | Product Catalog        |
| voucher-service      | `voucher-service-client-id`      | Voucher Management     |
| order-service        | `order-service-client-id`        | Order Processing       |
| notification-service | `notification-service-client-id` | Notifications          |

## 🗄️ Database Schema

### Users Table

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    user_type VARCHAR(50) DEFAULT 'REGULAR',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Service Credentials Table

```sql
CREATE TABLE service_credentials (
    id SERIAL PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    service_version VARCHAR(50),
    description TEXT,
    client_id VARCHAR(255) UNIQUE NOT NULL,
    client_key_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Bootstrap Tokens Table

```sql
CREATE TABLE bootstrap_tokens (
    id SERIAL PRIMARY KEY,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT false,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔒 Security Features

### Password Security

- **bcrypt Hashing**: All passwords hashed with bcrypt (cost factor 12)
- **Salt Generation**: Automatic salt generation for each password
- **Hash Verification**: Secure password verification during login

### JWT Security

- **Secret Key**: Configurable JWT secret key
- **Token Expiration**: Configurable token expiration (default 24 hours)
- **Claims Validation**: Comprehensive token claims validation
- **Secure Headers**: Proper JWT header validation

### Service Authentication

- **Client Credentials**: Unique client-id and client-key per service
- **Key Hashing**: Client keys hashed with bcrypt before storage
- **Bootstrap Tokens**: Time-limited bootstrap tokens for service registration
- **Access Control**: Method-level access control via gRPC middleware

## 📊 Monitoring and Observability

### Health Checks

```bash
# HTTP health check
curl http://localhost:8080/health

# gRPC health check
grpcurl -plaintext localhost:50051 auth.AuthService/HealthCheck
```

### Metrics

Prometheus metrics available at `/metrics`:

- **Authentication Metrics**: Login success/failure rates, token validation counts
- **Service Registry Metrics**: Service registration counts, credential validation rates
- **Database Metrics**: Connection pool stats, query performance
- **gRPC Metrics**: Request counts, response times, error rates
- **System Metrics**: Memory usage, CPU usage, goroutines

### Distributed Tracing

Jaeger tracing integration provides:

- Request tracing across authentication flows
- Database query tracing
- Service registration tracing
- Performance bottleneck identification

### Logging

Structured JSON logging includes:

- Authentication events (login, token validation)
- Service registration events
- Security events (failed authentications, invalid tokens)
- Database operations
- Error tracking

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
make test

# Run tests with coverage
go test -cover ./...

# Run specific test package
go test ./internal/service/...
```

### Integration Tests

```bash
# Run integration tests (requires database)
go test -tags=integration ./...
```

### gRPC Testing

```bash
# Test user login
grpcurl -plaintext -d '{"email":"<EMAIL>","password":"password"}' \
  localhost:50051 auth.AuthService/Login

# Test token validation
grpcurl -plaintext -d '{"token":"your-jwt-token"}' \
  localhost:50051 auth.AuthService/ValidateToken

# Test service registration
grpcurl -plaintext -d '{"service_name":"test-service","service_version":"1.0.0","description":"Test service","bootstrap_token":"your-bootstrap-token"}' \
  localhost:50051 auth.AuthService/RegisterService

# Test service credential validation
grpcurl -plaintext -d '{"client_id":"your-client-id","client_key":"your-client-key"}' \
  localhost:50051 auth.AuthService/ValidateServiceCredentials
```

## 🔧 Development

### Project Structure

```
coupon-auth-service/
├── cmd/
│   ├── server/
│   │   └── main.go              # Main server application
│   └── cli/
│       └── main.go              # CLI tool for service registration
├── internal/
│   ├── service/
│   │   └── auth_service.go      # Core authentication service logic
│   ├── repository/
│   │   ├── user_repository.go   # User data access layer
│   │   └── service_repository.go # Service credential data access
│   ├── model/
│   │   ├── user.go              # User data model
│   │   ├── service_credential.go # Service credential model
│   │   └── bootstrap_token.go   # Bootstrap token model
│   ├── middleware/
│   │   └── grpc_auth.go         # gRPC authentication middleware
│   └── utils/
│       ├── jwt.go               # JWT utilities
│       ├── password.go          # Password hashing utilities
│       └── bootstrap.go         # Bootstrap token utilities
├── config/
│   └── config.yaml              # Service configuration
├── scripts/                     # Deployment and utility scripts
├── Dockerfile                   # Container image definition
├── docker-compose.yml           # Local development setup
├── Makefile                     # Build and deployment commands
└── README.md                    # This file
```

### Makefile Targets

#### Development Targets

- `build` - Build the auth server binary
- `build-cli` - Build the CLI tool for service registration
- `run` - Build and run the auth server
- `test` - Run all tests
- `docker-build` - Build Docker image
- `compose-up` - Start services with Docker Compose
- `compose-down` - Stop Docker Compose services

#### CI/CD Service Registration

- `register-service` - Register a service with the auth service (simulates CI/CD pipeline)

### Adding New Authentication Methods

1. **Define gRPC Method**: Add method to `auth.proto`
2. **Implement Service**: Add method to `auth_service.go`
3. **Add Repository Method**: Implement data access if needed
4. **Add Middleware**: Update gRPC middleware for access control
5. **Add Tests**: Write unit and integration tests

### Database Migrations

The service uses GORM auto-migration:

```go
// Auto-migrate database schema
db.AutoMigrate(&model.User{}, &model.ServiceCredential{}, &model.BootstrapToken{})
```

For production deployments, consider using explicit migrations.

## 🚨 Error Handling

### gRPC Error Codes

The service returns appropriate gRPC status codes:

- `OK` - Successful operation
- `INVALID_ARGUMENT` - Invalid request parameters
- `UNAUTHENTICATED` - Authentication failed
- `PERMISSION_DENIED` - Authorization failed
- `NOT_FOUND` - Resource not found
- `ALREADY_EXISTS` - Resource already exists
- `INTERNAL` - Internal server error
- `UNAVAILABLE` - Service temporarily unavailable

### Error Response Format

```json
{
  "code": "UNAUTHENTICATED",
  "message": "Invalid credentials",
  "details": [
    {
      "type": "authentication_error",
      "field": "password",
      "description": "Password does not match"
    }
  ]
}
```

## 🔒 Security Best Practices

### Password Security

- Use strong password policies in client applications
- Implement rate limiting for login attempts
- Monitor for suspicious authentication patterns
- Regular password rotation recommendations

### JWT Security

- Use strong, randomly generated JWT secrets
- Implement proper token expiration
- Consider token refresh mechanisms
- Monitor for token abuse

### Service Security

- Rotate service credentials regularly
- Monitor service authentication patterns
- Implement proper access logging
- Use secure communication channels (TLS)

### Bootstrap Token Security

- Use time-limited bootstrap tokens
- Implement single-use bootstrap tokens
- Secure bootstrap token distribution
- Monitor bootstrap token usage

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**

   ```bash
   # Check database connectivity
   psql -h localhost -p 5432 -U postgres -d coupon_auth
   ```

2. **JWT Token Invalid**

   ```bash
   # Verify JWT secret configuration
   echo $JWT_SECRET

   # Check token expiration
   jwt-cli decode your-token-here
   ```

3. **Service Registration Failed**

   ```bash
   # Verify bootstrap token
   echo $BOOTSTRAP_TOKEN

   # Check bootstrap token expiry
   date -d "2024-12-31T23:59:59Z"
   ```

4. **gRPC Connection Issues**
   ```bash
   # Test gRPC connectivity
   grpcurl -plaintext localhost:50051 list
   ```

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=debug
make run
```

### Database Debugging

```bash
# Check database tables
psql -h localhost -p 5432 -U postgres -d coupon_auth -c "\dt"

# Check service credentials
psql -h localhost -p 5432 -U postgres -d coupon_auth -c "SELECT service_name, client_id, is_active FROM service_credentials;"
```

## 📚 Additional Resources

- [gRPC Go Documentation](https://grpc.io/docs/languages/go/)
- [GORM Documentation](https://gorm.io/docs/)
- [JWT Go Library](https://github.com/golang-jwt/jwt)
- [bcrypt Documentation](https://pkg.go.dev/golang.org/x/crypto/bcrypt)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Jaeger Tracing](https://www.jaegertracing.io/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
