package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	"github.com/labstack/echo/v4"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/service"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, cfg.Logging.Format)
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	metricsManager, err := metrics.NewManager(cfg, cfg.Service.Name, logger)
	if err != nil {
		logger.Fatalf("failed to initialize metrics manager: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	metricsManager.Start(ctx)

	appMetrics := metricsManager.Metrics

	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics, cfg.Service.Name)
	if err != nil {
		logger.Fatalf("failed to connect to database: %v", err)
	}

	autoMigrator := database.NewAutoMigrator(db, logger)
	models := []any{&model.ServiceCredential{}}

	if err := autoMigrator.AutoMigrate(models...); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}

	repo := repository.NewAuthRepository(db)
	svc := service.NewAuthService(repo, logger, cfg)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		authFunc := middleware.CreateServiceAuthFunc(repo, cfg)
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, authFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, logger, healthChecker, appMetrics)
	}()

	wg.Wait()
	logger.Info("Shutting down gracefully...")
	metricsManager.Stop()
	db.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.AuthService, authFunc grpc_auth.AuthFunc) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc, cfg.Service.Name)
	grpcHandler := grpc_handler.NewAuthServer(svc)
	proto_auth_v1.RegisterAuthServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

func startHTTPServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, healthChecker *health.HealthChecker, metrics *metrics.Metrics) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET("/metrics", echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}
